using Microsoft.AspNetCore.Mvc;
using Cerebrum.BLL.Timesheet;
using Cerebrum.ViewModels.Timesheet;
using Cerebrum30.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Cerebrum30.ViewComponents
{
    public class WorkingHoursSearchViewComponent : ViewComponent
    {
        private readonly IUserTimesheetBLL _bll;

        public WorkingHoursSearchViewComponent(IUserTimesheetBLL bll)
        {
            _bll = bll;
        }

        public async Task<IViewComponentResult> InvokeAsync()
        {
            // Get the current user context (similar to how the controller does it)
            var cerebrumUser = HttpContext.Items["CerebrumUser"] as dynamic;

            var tss = new VMWorkingHoursSearch();
            tss.Offices = new List<VMOfficeTS>() { new VMOfficeTS() { Id = 0, OfficeName = "All Offices" } };

            if (cerebrumUser != null)
            {
                var officesAndUsers = _bll.OfficeUsers(cerebrumUser.PracticeId, 0);
                tss.Offices.AddRange(officesAndUsers.Offices);
                tss.OfficeId = 0;
                var users = officesAndUsers.Users;

                if (!cerebrumUser.HasPermission("WorkingHoursAdmin"))
                {
                    int currentUserId = cerebrumUser.UserId;
                    users = users.Where((Func<dynamic, bool>)(w => w.UserId == currentUserId)).ToList();
                }

                tss.Users = users.AsEnumerable();
            }

            tss.FromDate = DateTime.Today;
            tss.ToDate = DateTime.Today;

            return View(tss);
        }
    }
}
