@using Cerebrum.ViewModels.Patient
﻿@model IEnumerable<Cerebrum.ViewModels.Patient.VMPatientRecordMessage>
<script>
    $(document).ready(function () {
       
@* }); *@
    function TakeAction(elementId, targetid) {



        $.get({ URL, serializedData }).done(function () {
@* $(targetid).addClass("done"); *@
@* }); *@


</script>

<table class="table">
    <tr>
        
        <th>
            @Html.DisplayNameFor(model => model.PatientLastName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.Message)
        </th>
        <th></th>
    </tr>

@foreach (var item in Model) {
    <tr>
        <td>
            @Html.DisplayFor(modelItem => item.PatientLastName)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.Message)
        </td>
       
        <td>
            @Html.ActionLink("<PERSON> Seen", "MessageSeen")

           new {





           }) 
        </td>
    </tr>


</table>
