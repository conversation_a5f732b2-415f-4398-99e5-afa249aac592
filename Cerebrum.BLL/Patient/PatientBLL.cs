﻿using Cerebrum.BLL.Patient.SPEntities;
using Cerebrum.BLL.Schedule;
using Cerebrum.BLL.Utility;
using Cerebrum.Data;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.ViewModels.Common;
using Cerebrum.ViewModels.Patient;
using Cerebrum.ViewModels.VP;
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore; // EF Core: Replace System.Data.Entity
using Microsoft.Data.SqlClient; // EF Core: Replace System.Data.SqlClient
using System.Linq;
using System.Transactions;
using Cerebrum.BLL.Common;
using Cerebrum.DTO.Patient.SPDtos;
using Cerebrum.DTO.Patient;
using AwareMD.Cerebrum.Shared;
using System.Globalization;
using Cerebrum.ViewModels.Medications;
using Cerebrum.ViewModels.Documents;
using Cerebrum.BLL.ExternalDoctors;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace Cerebrum.BLL.Patient
{
    public interface IPatientBLL
    {
        List<VMLookupItem> SearchPatients(string term_, int practiceId);
        List<VMLookupItem> SearchPracticeDoctors(string term_, int practiceId);
        List<VMLookupItem> SearchPracticeUsers(string term_, int practiceId);
        List<VMPatientAppointment> GetPatientAppointments(int patientRecordId);
        List<VMPatientSearchResult> GetTopPracticePatients(int practiceId, string searchString);
        List<VMPatientSearchResult> GetTopPracticePatients(int practiceId, string searchString, int searchType);
        List<VMPatientInfo> GetPracticePatients(int practiceId, string searchString, int searchType = 0);
        List<VMPatientInfo> GetPracticePatients(int practiceId, string searchString, int? active, int topResults = 20, int searchType = 0);
        List<VMPatientInfo> GetPracticePatientsOldChartNumbers(int practiceId, string searchString);
        PatientDemographicInfoDTO GetPatientDemographicInfo(int practiceId, int patientRecordId);
        VMPatientInfo GetPatientInfo(int patientId, int activeStatus = 0);
        VMPatientInfo GetPatientInfo(int patientId, int appointmentID, int testID, int activeStatus = 0);
        VMPatientNotesMain GetPatientNotesMain(int patientId, int practiceId);
        List<VMPatientNote> GetPatientNotes(int patientId, int practiceId);
        int GetPatientIdByAppointmentInfo(int Id, bool isAppId = false);
        VMPatientMedicationChart GetPatientInfoWithMedication(int patientId);
        VMPatientDemo SaveNewDemographics(VMPatientDemo model, int userid, string ipaddress);
        List<ValueText1> GetCohortsByPatientRecordId(int patientId, int practiceId);
        VMPatientDemo PopulatePatientEdit(int patientRecordId, int practiceId, int appointmentId = 0, int appReferrealDoctorId = 0);
        VMPatientDemo UpdateDemographics(VMPatientDemo retModel, int userid, string ipaddress);
        VMExternalDoctorReportContact GetPatientReportContact(VMExtDocRepContactRequest request, int userId, string ipAddress);
        void UpdatePatientReportContact(VMSetExDocReportContact patientRepContact, int userId, string ipAddress);
        void ResetPatientReportContact(int appointmentId, bool isPhone, int userId, string ipAddress);
        void UpdatePatientAppointmentsLocations(VMSetExDocReportContact exDocRepContact, int userId, string ipAddress);
        VMPatientLocation GetActivePatientLocation(int patientId, int externalDoctorId);
        bool CheckNamesDOBCollusion(CheckDate data);
        string ChangeStatus(int activeId, int patientRecordId, int userid, string ipaddress);
        List<VMLookupItem> SearchMainDocs(string term_, int practiceId);
        List<VMLookupItem> SearchDemographicsDocs(string term_);
        List<ValueTextBool> GetFamilyDocListAfterActiveation(PtnDocIds ptnDocIds);
        List<ValueTextBool> GetFamilyDocListAfterRemoval(PtnDocIds ptnDocIds);
        DoctorsData GetDemographicsReferralDoctor(ActivateDemographicsReferralDoctorData request, int modifyingUserId, string ipAddress);
        List<ValueTextBool> GetAssocDocListAfterActiveation(PtnDocIds ptnDocIds);
        List<ValueTextBool> GetAssocDocListAfterRemoval(PtnDocIds ptnDocIds);
        bool IsPatientHasAlert(int patientId);
        ContactQuery AddNewContact(contactToSend contact, int userid, string ipaddress);
        ContactQuery EditContact(contactToSend contact, int userid, string ipaddress);
        ContactQuery EditContactPhone(contactToSend contact, int userid, string ipaddress);
        ContactQuery NewContactPhone(contactToSend contact, int userid, string ipaddress);
        mrnToSend UpdatePatientsMRN(mrnToSend mrnInfo, int userid, string ipaddress);
        PhoneQuery AddNewPPhone(PhoneDataTransfer phInfo, int userid, string ipaddress);
        PhoneQuery EditPPhone(PhoneDataTransfer phInfo, int userid, string ipaddress);
        string DeletePatientCohort(int patCohId_int);
        Cerebrum.ViewModels.Cohort.PatientCohortVM GetDemCohort(int patientRecordId, int practiceId);
        Cerebrum.ViewModels.Cohort.PatientCohortVM GetCohortClassByDoctor(int practiceDoctorId, int practiceId);
        VM_DemCohort SaveDemCohort(VM_DemCohort demModel, int userid, string ipaddress);
        VM_DemAddress SaveNewDemAddress(VM_DemAddress demModel, int userid, string ipaddress);
        VM_DemAddress UpdateDemAddress(VM_DemAddress demModel, int userid, string ipaddress);
        VM_DemAddress GetDemAddress(VM_DemAddress demModel);
        VM_Enrollment_n SaveNewEnrollment(VM_Enrollment_n demModel, int userid, string ipaddress);
        VM_Enrollment_n UpdateEnrollment(VM_Enrollment_n demModel, int userid, string ipaddress);
        VM_Enrollment_n GetEnrollment(VM_Enrollment_n demModel);
        VM_Enrollment_n GetEnrollmentList(VM_Enrollment_n demModel);
        VMReportPatient GetReportPatient(int patientId);
        VMPatient GetPatientInfoForMed(int patientId);
    }
    public class PatientBLL : IPatientBLL, IDisposable
    {
        private CerebrumContext context;
        private LanguageBLL _languageBll;
        private IExternalDoctorBLL _externalDoctorBll;
        private readonly log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public PatientBLL(CerebrumContext context)
        {
            this.context = context;
            _externalDoctorBll = new ExternalDoctorBLL(context);
            _languageBll = new LanguageBLL(context);
        }
        public List<VMPatientAppointment> GetPatientAppointments(int patientRecordId)
        {
            var schbll = new AppointmentsBLL();
            var apps = new List<VMPatientAppointment>();
            var dbaps = context.Appointments.Where(w => w.PatientRecordId == patientRecordId).ToList();
            if (dbaps != null)
            {
                dbaps = dbaps.OrderByDescending(t => t.appointmentTime).ToList();
            }

            foreach (var a in dbaps)
            {
                var pa = new VMPatientAppointment();

                pa.appointmentId = a.Id;
                pa.patientId = a.PatientRecordId;

                pa.appointmentDateTime = a.appointmentTime;
                pa.status = a.appointmentStatus;
                var office = context.Offices.FirstOrDefault(o => o.Id == a.OfficeId);
                pa.Office = office != null ? office.name : "";

                var pracdoc = context.PracticeDoctors.FirstOrDefault(pd => pd.Id == a.PracticeDoctorId);

                var externaldocp = pracdoc != null ? context.ExternalDoctors.FirstOrDefault(ex => ex.Id == pracdoc.ExternalDoctorId) : null;
                pa.Doctor = externaldocp != null ? externaldocp.FullName : "";

                var externaldoc = context.ExternalDoctors.FirstOrDefault(ex => ex.Id == a.referralDoctorId);
                pa.RefDoctor = externaldoc != null ? externaldoc.FullName : "";

                var testBuilder = new List<string>();

                var apptests = a.appointmentTests;//.Select(s => s.TestId);
                var patientappTests = new List<VMPatientAppointmentTest>();
                foreach (var tb in apptests)
                {
                    var test = context.Tests.Find(tb.TestId);

                    testBuilder.Add(test.testShortName);

                    patientappTests.Add(new VMPatientAppointmentTest { Id = tb.Id, visitDate = tb.startTime, testShortName = test.testShortName, testFullName = test.testFullName });

                }
                pa.internalTests = patientappTests;

                pa.Tests = string.Join(",", testBuilder);


                var appBill = context.AppointmentBill.AsNoTracking().Where(ab => ab.AppointmentID == a.Id);
                if (appBill != null)
                {
                    var consultCodes = appBill.Select(s => s.ConsultCode).ToList();
                    if (consultCodes != null && consultCodes.Count() > 0)
                    {
                        var lstDiagnoseCode = context.ConsultCode.Where(c => consultCodes.Contains(c.Id))
                   .Select(x => new VMConsultCode()
                   {
                       Id = x.Id,
                       Code = x.Code,
                       Spec = x.Spec,
                       Name = x.Name,
                       Tech = x.Tech,
                       Pro = x.Pro,
                       Fee = x.Fee,
                       Note = x.Note,
                       SortOrder = x.SortOrder
                   }).ToList();

                        pa.ConsultCodes = string.Join(",", lstDiagnoseCode.Select(ss => ss.Code));

                        var dc = new List<string>();
                        foreach (var cl in appBill)
                        {
                            var digc = (from c in context.DiagnoseCode
                                        where c.Id == cl.DiagnosticCode || c.Id == cl.DiagnosticCode2 || c.Id == cl.DiagnosticCode3
                                        select c.Code).ToList();
                            dc.AddRange(digc);
                        }
                        pa.DiagCodes = string.Join(",", dc);
                    }
                }
                /// Changes made according to new requirement
                var history = schbll.GetAppointmentHistory(a.Id, a.PatientRecord.PracticeId);
                if (history != null && history.AppointmentChanges.Count() > 0)
                    pa.AppointmentChanges.AddRange(history.AppointmentChanges);

                apps.Add(pa);
            }
            return apps;
        }
        public List<VMPatientSearchResult> GetTopPracticePatients(int practiceId, string searchString)
        {
            if (IsNotAllowedSearchTerm(searchString))
                return new List<VMPatientSearchResult>();

            List<SqlParameter> parms = new List<SqlParameter>();
            List<Demographic> dbPatients = new List<Demographic>();
            var topResults = 20;
            var patients = new List<VMPatientSearchResult>();
            long n;
            bool isNumeric = long.TryParse(searchString, out n);
            if (isNumeric)
            {

                parms = new List<SqlParameter>
                {
                    new SqlParameter("PracticeId",practiceId),
                    new SqlParameter("LastName",null),
                    new SqlParameter("FirstName",null),
                    new SqlParameter("OHIP",searchString),
                    new SqlParameter("TOPResult",topResults),
                    new SqlParameter("Active","0"),
                    new SqlParameter("YearOfBirth",null),
                    new SqlParameter("PhoneNumber",null),
                    new SqlParameter("PatientId",null),
                    new SqlParameter("DateOfBirth",null)
                };

            }
            else if (searchString.Contains(","))
            {
                string[] names = searchString.Split(',');
                string lastname = names[0].Trim();
                string firstname = names[1].Trim();
                int? yearOfBirth = GetYearOfBirth(names);

                if ((!string.IsNullOrWhiteSpace(lastname)) && (string.IsNullOrWhiteSpace(firstname)))
                {
                    parms = new List<SqlParameter>
                    {
                        new SqlParameter("PracticeId",practiceId),
                        new SqlParameter("LastName",lastname),
                        new SqlParameter("FirstName",null),
                        new SqlParameter("OHIP",null),
                        new SqlParameter("TOPResult",topResults),
                        new SqlParameter("Active","0"),
                        new SqlParameter("YearOfBirth",null),
                        new SqlParameter("PhoneNumber",null),
                        new SqlParameter("PatientId",null),
                        new SqlParameter("DateOfBirth",null)
                    };

                }
                else if ((string.IsNullOrWhiteSpace(lastname)) && (!string.IsNullOrWhiteSpace(firstname)))
                {
                    parms = new List<SqlParameter>
                    {
                        new SqlParameter("PracticeId",practiceId),
                        new SqlParameter("LastName",null),
                        new SqlParameter("FirstName",firstname),
                        new SqlParameter("OHIP",null),
                        new SqlParameter("TOPResult",topResults),
                        new SqlParameter("Active","0"),
                        new SqlParameter("YearOfBirth",null),
                        new SqlParameter("PhoneNumber",null),
                        new SqlParameter("PatientId",null),
                        new SqlParameter("DateOfBirth",null)
                    };

                }
                else
                {
                    parms = new List<SqlParameter>
                    {
                        new SqlParameter("PracticeId",practiceId),
                        new SqlParameter("LastName",lastname),
                        new SqlParameter("FirstName",firstname),
                        new SqlParameter("OHIP",null),
                        new SqlParameter("TOPResult",topResults),
                        new SqlParameter("Active","0"),
                        new SqlParameter("YearOfBirth",yearOfBirth),
                        new SqlParameter("PhoneNumber",null),
                        new SqlParameter("PatientId",null),
                        new SqlParameter("DateOfBirth",null)
                    };

                }
            }
            else
            {
                parms = new List<SqlParameter>
                {
                    new SqlParameter("PracticeId",practiceId),
                    new SqlParameter("LastName",searchString),
                    new SqlParameter("FirstName",searchString),
                    new SqlParameter("OHIP",null),
                    new SqlParameter("TOPResult",topResults),
                    new SqlParameter("Active","0"),
                    new SqlParameter("YearOfBirth",null),
                    new SqlParameter("PhoneNumber",null),
                    new SqlParameter("PatientId",null),
                    new SqlParameter("DateOfBirth",null)
                };

            }

            patients = GetPatientSearchResult(parms);

            return patients;
        }

        public List<VMPatientSearchResult> GetTopPracticePatients(int practiceId, string searchString, int searchType)
        {
            if (IsNotAllowedSearchTerm(searchString))
                return new List<VMPatientSearchResult>();

            List<SqlParameter> parms = new List<SqlParameter>();

            var topResults = 20;
            var patients = new List<VMPatientSearchResult>();
            // 0 - search as before, 1 - Name, 2 - OHIP, 3 - Phone, 4 - PatientId, 5 - DOB

            if (searchType == 0)
            {
                return GetTopPracticePatients(practiceId, searchString);
            }
            else
            {
                parms = GetSearchParameters(practiceId, searchString, searchType, topResults, 0);

            }

            if (parms.Count == 0)
            {
                return new List<VMPatientSearchResult>();
            }

            patients = GetPatientSearchResult(parms);

            return patients;
        }
        public List<VMPatientInfo> GetPracticePatients(int practiceId, string searchString, int searchType = 0)
        {
            return GetPracticePatients(practiceId, searchString, 0, 20, searchType);
        }
        public List<VMPatientInfo> GetPracticePatients(int practiceId, string searchString, int? active, int topResults)
        {
            if (IsNotAllowedSearchTerm(searchString))
                return new List<VMPatientInfo>();

            List<SqlParameter> parms = new List<SqlParameter>();
            var patients = new List<VMPatientInfo>();
            long n;
            bool isNumeric = long.TryParse(searchString, out n);
            if (isNumeric)
            {
                parms = new List<SqlParameter>
                {
                    new SqlParameter("PracticeId",practiceId),
                    new SqlParameter("LastName",null),
                    new SqlParameter("FirstName",null),
                    new SqlParameter("OHIP",searchString),
                    new SqlParameter("TOPResult",topResults),
                    new SqlParameter("Active",active),
                    new SqlParameter("YearOfBirth",null),
                    new SqlParameter("PhoneNumber",null),
                    new SqlParameter("PatientId",null),
                    new SqlParameter("DateOfBirth",null)
                };

            }
            else if (searchString.Contains(","))
            {
                string[] names = searchString.Split(',');
                string lastname = names[0].Trim();
                string firstname = names[1].Trim();
                int? yearOfBirth = GetYearOfBirth(names);

                if ((!string.IsNullOrWhiteSpace(lastname)) && (string.IsNullOrWhiteSpace(firstname)))
                {
                    parms = new List<SqlParameter>
                    {
                        new SqlParameter("PracticeId",practiceId),
                        new SqlParameter("LastName",lastname),
                        new SqlParameter("FirstName",null),
                        new SqlParameter("OHIP",null),
                        new SqlParameter("TOPResult",topResults),
                        new SqlParameter("Active",active),
                        new SqlParameter("YearOfBirth",null),
                        new SqlParameter("PhoneNumber",null),
                        new SqlParameter("PatientId",null),
                        new SqlParameter("DateOfBirth",null)
                    };

                }
                else if ((string.IsNullOrWhiteSpace(lastname)) && (!string.IsNullOrWhiteSpace(firstname)))
                {
                    parms = new List<SqlParameter>
                    {
                        new SqlParameter("PracticeId",practiceId),
                        new SqlParameter("LastName",null),
                        new SqlParameter("FirstName",firstname),
                        new SqlParameter("OHIP",null),
                        new SqlParameter("TOPResult",topResults),
                        new SqlParameter("Active",active),
                        new SqlParameter("YearOfBirth",null),
                        new SqlParameter("PhoneNumber",null),
                        new SqlParameter("PatientId",null),
                        new SqlParameter("DateOfBirth",null)
                    };

                }
                else
                {
                    parms = new List<SqlParameter>
                    {
                        new SqlParameter("PracticeId",practiceId),
                        new SqlParameter("LastName",lastname),
                        new SqlParameter("FirstName",firstname),
                        new SqlParameter("OHIP",null),
                        new SqlParameter("TOPResult",topResults),
                        new SqlParameter("Active",active),
                        new SqlParameter("YearOfBirth",yearOfBirth),
                        new SqlParameter("PhoneNumber",null),
                        new SqlParameter("PatientId",null),
                        new SqlParameter("DateOfBirth",null)
                    };

                }
            }
            else
            {
                parms = new List<SqlParameter>
                    {
                        new SqlParameter("PracticeId",practiceId),
                        new SqlParameter("LastName",searchString),
                        new SqlParameter("FirstName",searchString),
                        new SqlParameter("OHIP",null),
                        new SqlParameter("TOPResult",topResults),
                        new SqlParameter("Active",active),
                        new SqlParameter("YearOfBirth",null),
                        new SqlParameter("PhoneNumber",null),
                        new SqlParameter("PatientId",null),
                        new SqlParameter("DateOfBirth",null)
                    };
            }

            var dbPatients = GetPatientSearchResult(parms);
            foreach (var demographics in dbPatients)
            {
                var patient = new VMPatientInfo();
                patient.PatientId = demographics.PatientId;
                patient.FirstName = demographics.FirstName;
                patient.LastName = demographics.LastName;
                patient.MiddleName = demographics.MiddleName;
                patient.FullName = demographics.FullName;
                patient.DateOfBirth = demographics.DateOfBirth;
                patient.PracticeId = practiceId;
                patient.OHIP = demographics.OHIP;
                patient.OHIPVersionCode = demographics.OHIPVersionCode;
                patient.DefaultPaymentMethod = demographics.DefaultPaymentMethod;
                patient.StatusStr = demographics.Active.ToString();

                patients.Add(patient);
            }
            return patients;
        }

        public List<VMPatientInfo> GetPracticePatients(int practiceId, string searchString, int? active = 0, int topResults = 20, int searchType = 0)
        {
            if (IsNotAllowedSearchTerm(searchString))
                return new List<VMPatientInfo>();

            List<SqlParameter> parms = new List<SqlParameter>();
            var patients = new List<VMPatientInfo>();
            // 0 - as before, needs to revise all search functions.
            if (searchType == 0)
            {
                // as before
                return GetPracticePatients(practiceId, searchString, active, topResults);
            }
            // 1 - Name, 2 - OHIP, 3 - Phone, 4 - PatientId, 5 - DOB
            else
            {
                parms = GetSearchParameters(practiceId, searchString, searchType, topResults, active);
            }
            if (parms.Count == 0)
            {
                return new List<VMPatientInfo>();
            }

            var dbPatients = GetPatientSearchResult(parms);
            foreach (var demographics in dbPatients)
            {
                var patient = new VMPatientInfo();
                patient.PatientId = demographics.PatientId;
                patient.FirstName = demographics.FirstName;
                patient.LastName = demographics.LastName;
                patient.MiddleName = demographics.MiddleName;
                patient.FullName = demographics.FullName;
                patient.DateOfBirth = demographics.DateOfBirth;
                patient.PracticeId = practiceId;
                patient.OHIP = demographics.OHIP;
                patient.OHIPVersionCode = demographics.OHIPVersionCode;
                patient.DefaultPaymentMethod = demographics.DefaultPaymentMethod;
                patient.StatusStr = demographics.Active.ToString();

                patients.Add(patient);
            }
            return patients;
        }
        public List<VMPatientInfo> GetPracticePatientsOldChartNumbers(int practiceId, string searchString)
        {

            var dbPatients = new List<SP_PatientLegacySearch>();
            var topResults = 500;
            var patients = new List<VMPatientInfo>();

            if (practiceId > 0 && (!string.IsNullOrWhiteSpace(searchString)))
            {
                List<SqlParameter> parms = new List<SqlParameter>()
                    {
                        new SqlParameter("PracticeId",practiceId),
                        new SqlParameter("chartNumber",searchString),
                        new SqlParameter("topResult",topResults)
                    };


                dbPatients = context.GetData<SP_PatientLegacySearch>("[dbo].[SearchPatientsByOldChartNumber]", parms).ToList();
            }


            foreach (var demographics in dbPatients)
            {
                var patient = new VMPatientInfo();
                patient.PatientId = demographics.PatientId;
                patient.FirstName = demographics.FirstName;
                patient.LastName = demographics.LastName;
                patient.MiddleName = demographics.MiddleName;
                patient.FullName = demographics.LastName + ", " + demographics.FirstName + " " + demographics.MiddleName;
                patient.DateOfBirth = demographics.DateOfBirth;
                patient.PracticeId = practiceId;
                patient.StatusId = (int)demographics.Active;
                patient.StatusStr = demographics.Active.ToString();
                patient.MRN = demographics.UniqueChartNumber;
                patient.OHIP = demographics.OHIP;
                patient.OHIPVersionCode = demographics.OHIPVersionCode;

                patients.Add(patient);
            }
            return patients;
        }
        public VMPatientInfo GetPatientInfo(int patientId, int activeStatus = 0) // 0 = Active, 1 = NotActive, 2 = Other, 3 = Deceased
        {
            var patientInfo = GetPatientInfoSP(patientId, activeStatus: activeStatus);
            return patientInfo;
        }

        /// 0 = Active, 1 = NotActive, 2 = Other, 3 = Deceased
        ///
        public VMPatientInfo GetPatientInfo(int patientId, int appointmentID, int testID, int activeStatus = 0)
        {
            var patientInfo = GetPatientInfoSP(patientId, appointmentID, testID, activeStatus);
            return patientInfo;
        }
        public VMPatientNotesMain GetPatientNotesMain(int patientId, int practiceId)
        {
            var main = new VMPatientNotesMain();

            //var patientInfo = GetPatientInfoSP(patientId, 0, 0,0);
            var notes = GetPatientNotes(patientId, practiceId);

            // main.PatientInfo = patientInfo;
            main.PatientId = patientId;
            main.PatientNotes = notes;
            return main;
        }
        public List<VMPatientNote> GetPatientNotes(int patientId, int practiceId)
        {
            var notes = new List<VMPatientNote>();

            var dbNotes = context.PatientAdditionalNotes
                .Include("PatientNoteType")
                .Include("PatientRecord")
                .Where(x => x.PatientRecordId == patientId
                && x.PatientRecord.PracticeId == practiceId
                && x.IsActive == true)
                .OrderByDescending(o => o.NoteDate)
                .ToList();

            foreach (var dbNote in dbNotes)
            {
                var note = new VMPatientNote();
                note.Id = dbNote.Id;
                note.Notes = dbNote.Notes;
                note.NoteDate = dbNote.NoteDate;
                note.PatientId = dbNote.PatientRecordId;
                note.PatientNoteTypeId = dbNote.PatientNoteTypeId;
                note.PatientNoteType = dbNote.PatientNoteType != null ? dbNote.PatientNoteType.NoteType : "";
                note.IsActive = dbNote.IsActive;

                notes.Add(note);
            }
            return notes;
        }
        public int GetPatientIdByAppointmentInfo(int Id, bool isAppId = false)
        {
            int patientId = 0;

            if (isAppId) // get by appointment id
            {

                var app = context.Appointments.Find(Id);
                if (app != null)
                {
                    patientId = app.PatientRecordId;
                }

            }
            else // get byt appointment test id
            {
                var app = (from a in context.Appointments
                           join t in context.AppointmentTests on a.Id equals t.AppointmentId
                           where t.Id == Id && a.IsActive && t.IsActive
                           select a).FirstOrDefault();

                if (app != null)
                {
                    patientId = app.PatientRecordId;
                }
            }
            return patientId;
        }
        public VMPatientMedicationChart GetPatientInfoWithMedication(int patientId)
        {
            var patientInfo = new VMPatientMedicationChart();
            var demographics = context.Demographics
                .Where(p => p.PatientRecordId == patientId)
                .OrderByDescending(o => o.Id).FirstOrDefault();

            if (demographics != null)
            {
                var healthCard = demographics.healthcards.OrderByDescending(o => o.Id).FirstOrDefault();

                patientInfo.PatientId = patientId;
                patientInfo.FullName = demographics.FullName;
                patientInfo.OHIP = healthCard != null ? healthCard.number : "";

            }
            return patientInfo;
        }
        public PatientDemographicInfoDTO GetPatientDemographicInfo(int practiceId, int patientRecordId)
        {
            PatientDemographicInfoDTO patientInfo = null;
            var parameters = new List<SqlParameter> { new SqlParameter("practiceId", practiceId) };
            parameters.Add(new SqlParameter("patientId", patientRecordId));
            try
            {

                IList<SP_PatientDemoInfo> dbInfo = context.GetData<SP_PatientDemoInfo>("[dbo].[SP_GetPatientDemographicInfo]", parameters).ToList();
                if (dbInfo != null)
                {
                    SP_PatientDemoInfo pdemo = dbInfo.FirstOrDefault();
                    patientInfo = pdemo.Map_PatientInfo_SP();
                }
            }
            catch (Exception ex)
            {
                var error = ex.Message;
            }
            return patientInfo;
        }
        ///0 = Active, 1 = NotActive, 2 = Other, 3 = Deceased
        ///
        private VMPatientInfo GetPatientInfoSP(int patientId, int appointmentId = 0, int testId = 0, int activeStatus = 0)
        {
            VMPatientInfo patientInfo = null;
            var parameters = new List<SqlParameter> { new SqlParameter("patientId", patientId) };
            parameters.Add(new SqlParameter("activeStatus", activeStatus));
            if (appointmentId > 0)
            {
                parameters.Add(new SqlParameter("appointmentId", appointmentId));
            }

            if (testId > 0)
            {
                parameters.Add(new SqlParameter("testId", testId));
            }

            var dbInfo = context.GetData<SP_PatientInfo>("[dbo].[GetPatientInfo]", parameters).ToList();

            if (dbInfo != null && dbInfo.Any())
            {
                var dbPatientInfo = dbInfo.First();
                patientInfo = new VMPatientInfo();
                patientInfo.Salutation = dbPatientInfo.Salutation;
                patientInfo.PracticeId = dbPatientInfo.PracticeId;
                patientInfo.DemographicId = dbPatientInfo.DemographicId;
                patientInfo.PatientId = dbPatientInfo.PatientRecordId;
                patientInfo.FirstName = dbPatientInfo.firstName;
                patientInfo.LastName = dbPatientInfo.lastName;
                patientInfo.MiddleName = dbPatientInfo.middleName;
                patientInfo.FullName = dbPatientInfo.lastName + ", " + dbPatientInfo.firstName;
                patientInfo.PreferredName = dbPatientInfo.preferredName;
                patientInfo.PatientPhoneNumbers = dbPatientInfo.PatientPhoneNumbers;
                patientInfo.Gender = dbPatientInfo.gender;
                patientInfo.DefaultPaymentMethod = dbPatientInfo.DefaultPaymentMethod;
                patientInfo.DateOfBirth = dbPatientInfo.dateOfBirth; // set date of birth first before getting age
                patientInfo.Age = patientInfo.PatientAge.ToString();
                patientInfo.AgeAccurate = dbPatientInfo.AgeAccurate;
                patientInfo.OHIP = dbPatientInfo.OHIP;
                patientInfo.OHIPVersionCode = dbPatientInfo.OHIPVersionCode;
                patientInfo.HealthCardProvince = dbPatientInfo.HealthCardProvince;
                patientInfo.FamilyDoctor = dbPatientInfo.FamilyDoctor;
                patientInfo.ReferralDoctor = !String.IsNullOrWhiteSpace(dbPatientInfo.AppointmentReferralDoctor) ? dbPatientInfo.AppointmentReferralDoctor : dbPatientInfo.DefaultReferralDoctor;
                patientInfo.MRPDoctor = dbPatientInfo.MRP;
                patientInfo.AppointmentDate = dbPatientInfo.appointmentTime;
                patientInfo.ActionOnAbnormal = dbPatientInfo.actionOnAbnormal != null ? (bool)dbPatientInfo.actionOnAbnormal : false;
                patientInfo.TestName = dbPatientInfo.TestName;
                patientInfo.TestReferralDoctor = dbPatientInfo.TestReferralDoctor;
                patientInfo.AppointmentID = appointmentId;
                patientInfo.PriorityName = dbPatientInfo.PriorityName;
            }

            return patientInfo;
        }
        public List<VMLookupItem> SearchPatients(string term_, int practiceId)
        {
            List<VMLookupItem> listItems = null;
            try
            {
                listItems = (from d in context.Demographics
                             join pr in context.PatientRecords on d.PatientRecordId equals pr.Id
                             where ((d.lastName == null ? "" : d.lastName.ToLower()) + ", " + (d.firstName == null ? "" : d.firstName.ToLower())).StartsWith(term_.ToLower()) &&
                             pr.PracticeId == practiceId
                             select new VMLookupItem
                             {
                                 Text = (d.lastName == null ? "" : d.lastName.ToLower()) + ", " + (d.firstName == null ? "" : d.firstName.ToLower()),
                                 Value = pr.Id.ToString()
                             }).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### PatientBLL.SearchPatients";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message;

                _log.Error(Msg);
            }

            return listItems;
        }
        public List<VMLookupItem> SearchPracticeDoctors(string term_, int practiceId)
        {
            List<VMLookupItem> listItems = null;
            try
            {
                listItems = (from ed in context.ExternalDoctors
                             join pd in context.PracticeDoctors on ed.Id equals pd.ExternalDoctorId
                             where ((ed.lastName == null ? "" : ed.lastName.ToLower()) + ", " + (ed.firstName == null ? "" : ed.firstName.ToLower())).StartsWith(term_.ToLower()) &&
                             pd.PracticeId == practiceId
                             select new VMLookupItem
                             {
                                 Text = (ed.lastName == null ? "" : ed.lastName.ToLower()) + ", " + (ed.firstName == null ? "" : ed.firstName.ToLower()),
                                 Value = pd.Id.ToString()
                             }).ToList();
            }
            catch (Exception ex)
            {
                string Msg = $" ### SearchPracticeDoctors {ex.ExceptionDetails()}";
                _log.Error(Msg);
            }

            return listItems;
        }
        public List<VMLookupItem> SearchPracticeUsers(string term_, int practiceId)
        {
            List<VMLookupItem> listItems = null;
            try
            {
                //var v = _userManager.Users.ToList();
                listItems = context.Users.Where(t => t.PracticeID == practiceId
               && (((t.LastName == null ? "" : t.LastName.ToLower()) + ", " + (t.FirstName == null ? "" : t.FirstName.ToLower())).StartsWith(term_.ToLower()))).
                Select(tt => new VMLookupItem
                {
                    Text = (tt.LastName == null ? "" : tt.LastName.ToLower()) + ", " + (tt.FirstName == null ? "" : tt.FirstName.ToLower()),
                    Value = tt.UserID.ToString()
                }).ToList();

            }
            catch (Exception ex)
            {
                string Msg = $" ### SearchPracticeDoctors {ex.ExceptionDetails()}";
                _log.Error(Msg);
            }

            return listItems;
        }
        #region for patient data
        public VMPatientDemo SaveNewDemographics(VMPatientDemo model, int userid, string ipaddress)
        {
            model.success = false;
            #region check
            //string ohip_ = (model.Ohip_1 ?? "") + (model.Ohip_2 ?? "") + (model.Ohip_3 ?? "");
            //if (ohip_ == "**********")
            //{
            //    bool isLN_FN_Exists = CheckLN_FN_Existence_P(model.LastName, model.FirstName);
            //    if (isLN_FN_Exists == true)
            //    {
            //        model.message = "Patient  " + model.LastName + ", " + model.FirstName + " already exists in database";
            //        return model;
            //    }
            //} 

            //if (model.skipOHIPCheck == true)
            //{
            //    bool isLN_FN_Exists = CheckLN_FN_Existence_P(model);
            //    if (isLN_FN_Exists == true)
            //    {
            //        model.message = "Patient with name: " + model.LastName + ", " + model.FirstName + " already exists in database";
            //        return model;
            //    }
            //}
            #endregion

            Demographic demographic = new Demographic();
            demographic.FederatedId = model.FederatedId;
            demographic.chartNumber = model.chartNumber;
            demographic.namePrefix = (Salutation)(model.SalutationId);
            demographic.firstName = model.FirstName;
            demographic.lastName = model.LastName;
            demographic.middleName = model.MiddleName;
            if (ValidationMethods.IsValidDate(model.DateOfBirth))
            {
                demographic.dateOfBirth = Convert.ToDateTime(model.DateOfBirth);
            }
            if (model.GenderId != null)
            {
                demographic.gender = (Gender)(model.GenderId);
            }
            demographic.aliasLastName = model.Alias;
            demographic.useAliases = model.UseAliases;
            demographic.SIN = model.SIN;
            demographic.hasEmail = model.hasEmail;
            demographic.email = model.hasEmail ? model.Email : string.Empty;
            demographic.consentEmail = (YesNo)(model.ConsentEmailId) == YesNo.NO ? false : true;
            demographic.preferredSpokenLanguage = Enum.GetName(typeof(Language), model.PreferedLanguageId);
            demographic.preferredOfficialLanguageSpecified = model.PreferredOfficialLanguageSpecified;
            demographic.OfficialLanguageId = model.OfficialLanguageId;//
            demographic.PreferredLanguageId = model.PreferedLanguageId;//
            demographic.defaultPaymentMethod = (PaymentMethod)(model.PaymentMethodId);
            demographic.insuranceType = (InsuranceKind)(model.InsuranceKindId);
            demographic.InsuranceCompanyId = model.InsuranceCompanyId;
            demographic.pharmacyFaxNumber = model.FaxPharmacy;
            demographic.active = (Active)(model.ActiveId);
            demographic.StatusDate = DateTime.Now.Date;
            demographic.notesAboutPatient = model.Notes;
            demographic.preferredName = model.PreferredName;
            demographic.pharmacyPhoneNumber = model.PhonePharmacy;
            demographic.CreatedDateTime = DateTime.Now;
            demographic.FavoritePharmacy = model.FavoritePharmacy;

            PatientRecord patientRecord = new PatientRecord();
            patientRecord.PracticeId = model.PracticeId;
            patientRecord.Demographics.Add(demographic);

            context.PatientRecords.Add(patientRecord);

            int patientRecordId_ = 0;
            int demographicId_ = 0;

            using (TransactionScope scope = new TransactionScope())
            {
                try
                {
                    context.SaveChanges(userid, ipaddress);
                    patientRecordId_ = patientRecord.Id;
                    demographicId_ = demographic.Id;
                    model.PatientRecordId = patientRecordId_;


                    #region Patient MRN
                    if (model.HospitalId > 0 && patientRecordId_ > 0)
                    {
                        if ((!string.IsNullOrWhiteSpace(model.MRN)) && model.HospitalId != 0)
                        {
                            PatientMRN ptntMRN = new PatientMRN();
                            ptntMRN.HospitalId = model.HospitalId;
                            ptntMRN.MedicalRecordNumber = model.MRN;
                            ptntMRN.PatientRecordId = patientRecordId_;
                            context.Entry(ptntMRN).State = EntityState.Added;
                        }
                    }
                    #endregion

                    #region Health Card
                    if (model.skipOHIPCheck == false)
                    {
                        DemographicsHealthCard ohipCard = new DemographicsHealthCard();
                        ohipCard.DemographicId = demographicId_;
                        ohipCard.provinceCode = (Province)(model.ProvinceHC_Id);

                        if (model.ProvinceHC_Id == 8)
                        {
                            ohipCard.number = model.Ohip;
                            bool isOhipExistsInDB = CheckOhipExistanceInDB(ohipCard.number, model.PracticeId);
                            if (isOhipExistsInDB)
                            {
                                model.message = "There is already patient in DB with ohip number: " + ohipCard.number + "  ! ";
                                return model;
                            }

                            if (ValidationMethods.IsValidDate(model.IssueDate))
                            {
                                ohipCard.dateIssued = Convert.ToDateTime(model.IssueDate);
                            }
                            if (ValidationMethods.IsValidDate(model.ExpiryDate))
                            {
                                ohipCard.expirydate = Convert.ToDateTime(model.ExpiryDate);
                            }
                            if (ValidationMethods.IsValidDate(model.ValidDate))
                            {
                                ohipCard.lastValidated = Convert.ToDateTime(model.ValidDate);
                            }
                            ohipCard.version = model.Version;
                            ohipCard.provinceCode = (Province)(model.ProvinceHC_Id);
                        }
                        else
                        {
                            bool isOhipExistsInDB = CheckOhipExistanceInDB(model.RMB, model.PracticeId);
                            if (isOhipExistsInDB)
                            {
                                model.message = "There is alrady patient in DB with ohip number: " + ohipCard.number + "  ! ";
                                return model;
                            }

                            ohipCard.number = model.RMB;
                            ohipCard.provinceCode = (Province)(model.ProvinceHC_Id);
                        }


                        context.Entry(ohipCard).State = EntityState.Added;
                    }
                    #endregion

                    #region Doctors

                    if (model.FamDoctorId > 0)
                    {
                        DemographicsFamilyDoctor famDoc = new DemographicsFamilyDoctor();
                        famDoc.DemographicId = demographicId_;
                        famDoc.ExternalDoctorId = model.FamDoctorId;
                        famDoc.IsActive = true;
                        famDoc.CreatedDate = DateTime.Now;
                        context.Entry(famDoc).State = EntityState.Added;
                    }

                    if (model.ReferralDoctorId > 0)
                    {
                        DemographicsDefaultReferralDoctor refDoc = new DemographicsDefaultReferralDoctor();
                        refDoc.DemographicId = demographicId_;
                        refDoc.ExternalDoctorId = model.ReferralDoctorId;
                        refDoc.CreatedDateTime = DateTime.Now;
                        context.Entry(refDoc).State = EntityState.Added;
                    }

                    if (model.AssociatedDoctorId > 0)
                    {
                        DemographicsAssociatedDoctor assosDoc = new DemographicsAssociatedDoctor();
                        assosDoc.DemographicId = demographicId_;
                        assosDoc.ExternalDoctorId = model.AssociatedDoctorId;
                        assosDoc.CreatedDateTime = DateTime.Now;
                        assosDoc.IsActive = true;
                        context.Entry(assosDoc).State = EntityState.Added;
                    }

                    //it's pracice doctor id --- MainDoctorId
                    DemographicsMainResponsiblePhysician mainDoc = null;
                    if (model.MainDoctorId > 0)
                    {
                        mainDoc = new DemographicsMainResponsiblePhysician();
                        mainDoc.DemographicId = demographicId_;
                        mainDoc.PracticeDoctorId = model.MainDoctorId;
                        mainDoc.IsActive = true;
                        mainDoc.ExternalDoctorId = context.PracticeDoctors.Where(t => t.Id == model.MainDoctorId)
                                                    .Select(tt => tt.ExternalDoctorId).First();

                        context.Entry(mainDoc).State = EntityState.Added;
                    }

                    #endregion

                    #region Phone
                    var phoneNumbers = new List<DemographicsPhoneNumber>();
                    if (model.Phone != null && model.Phone != "")
                    {
                        DemographicsPhoneNumber ph1 = new DemographicsPhoneNumber();
                        ph1.DemographicId = demographicId_;
                        ph1.phoneNumber = model.Phone;
                        ph1.typeOfPhoneNumber = PhoneNumberType.R;
                        ph1.notes = model.notesPh;
                        if (model.preferredPhoneId == 0)
                        {
                            ph1.IsActive = true;
                        }
                        ph1.extention = model.ExtentionPhone;

                        phoneNumbers.Add(ph1);
                    }
                    if (model.PhoneContact != null && model.PhoneContact != "")
                    {
                        DemographicsPhoneNumber ph2 = new DemographicsPhoneNumber();
                        ph2.DemographicId = demographicId_;
                        ph2.phoneNumber = model.PhoneContact;
                        ph2.typeOfPhoneNumber = PhoneNumberType.W;
                        if (model.preferredPhoneId == 2)
                        {
                            ph2.IsActive = true;
                        }
                        ph2.extention = model.ExtentionContact;

                        phoneNumbers.Add(ph2);
                    }
                    if (model.CellPhone != null && model.CellPhone != "")
                    {
                        DemographicsPhoneNumber ph3 = new DemographicsPhoneNumber();
                        ph3.DemographicId = demographicId_;
                        ph3.phoneNumber = model.CellPhone;
                        ph3.typeOfPhoneNumber = PhoneNumberType.C;
                        if (model.preferredPhoneId == 1)
                        {
                            ph3.IsActive = true;
                        }

                        phoneNumbers.Add(ph3);
                    }
                    // If we only have one phone number, make it the primary number
                    if (phoneNumbers.Count == 1)
                    {
                        phoneNumbers[0].IsActive = true;
                    }
                    context.PhoneNumber.AddRange(phoneNumbers);
                    #endregion

                    #region Address
                    if (model.Address != null && model.Address != "")
                    {
                        DemographicsAddress address = new DemographicsAddress();
                        address.DemographicId = demographicId_;
                        address.addressLine1 = model.Address;
                        address.addressType = (AddressTypes)model.AddressTypeId;
                        address.city = model.City;
                        address.country = Enum.GetName(typeof(Country), model.CountryId);
                        address.postalCode = model.PostalCode;
                        address.province = EnumExtensions.GetDisplayName((Province)model.ProvinceId);
                        address.IsActive = true;
                        address.IsRemoved = false;
                        context.DemographicsAddress.Add(address);
                    }

                    if (model.Address2 != null && model.Address2 != "")
                    {
                        DemographicsAddress address = new DemographicsAddress();
                        address.DemographicId = demographicId_;
                        address.addressLine1 = model.Address2;
                        address.addressType = (AddressTypes)model.AddressTypeId2;
                        address.city = model.City2;
                        address.country = Enum.GetName(typeof(Country), model.CountryId2);
                        address.postalCode = model.PostalCode2;
                        address.province = EnumExtensions.GetDisplayName((Province)model.ProvinceId2);
                        address.IsActive = true;
                        address.IsRemoved = false;
                        context.DemographicsAddress.Add(address);
                    }
                    #endregion

                    #region Next of Keen
                    if ((model.NextOfkin_LN != null && model.NextOfkin_LN != "") ||
                        (model.NextOfkin_FN != null && model.NextOfkin_FN != ""))
                    {
                        DemographicsNextOfKin nxk = new DemographicsNextOfKin();
                        nxk.DemographicId = demographicId_;
                        nxk.firstName = model.NextOfkin_LN;
                        nxk.lastName = model.NextOfkin_FN;
                        int contPurp = model.contactPurposeId ?? 0;
                        nxk.ContactPurposeEnum = (ContactPurpose)contPurp;

                        context.DemographicsContacts.Add(nxk);
                        context.SaveChanges(userid, ipaddress);

                        if (nxk.Id != 0 && model.Kin_phone != null && model.Kin_phone != "")
                        {
                            DemographicsContactPhoneNumber nxk_p = new DemographicsContactPhoneNumber();
                            nxk_p.DemographicsContactId = nxk.Id;
                            nxk_p.contactPhoneNumber = model.Kin_phone;
                            int contPhoneType = model.contactPhoneTypeId ?? 0;
                            nxk_p.typeOfPhoneNumber = (PhoneNumberType)contPhoneType;

                            context.DemographicsContactPhoneNumbers.Add(nxk_p);
                        }
                    }
                    #endregion

                    context.SaveChanges(userid, ipaddress);

                    #region Add Enrollment
                    if (mainDoc != null && mainDoc.Id != 0 && model.enrollDate != null)
                    {
                        if (model.termDate != null)
                        {
                            DemographicsEnrollment enroll = new DemographicsEnrollment();
                            enroll.DemographicsMRPId = mainDoc.Id;
                            enroll.enrollmentDate = model.enrollDate;
                            enroll.enrollmentTerminationDate = model.termDate;
                            int termRes = model.terminationReason ?? 0;
                            enroll.terminationReason = (TerminationReason)termRes;

                            context.SaveChanges(userid, ipaddress);
                        }
                        else
                        {
                            DemographicsEnrollment enroll = new DemographicsEnrollment();
                            enroll.DemographicsMRPId = mainDoc.Id;
                            enroll.enrollmentDate = model.enrollDate;

                            context.SaveChanges(userid, ipaddress);
                        }
                    }
                    #endregion

                    model.message = "Patient " + model.LastName + ", " + model.FirstName + " added to the database successfully !";
                    model.success = true;
                }
                catch (Exception ex)
                {
                    string Msg = $" ### PatientBLL.SaveNewDemographics {ex.ExceptionDetails()}";

                    _log.Error(Msg);

                    string message = "There was a problem to enter new patient. Contact Administrator! ";
                    model.message = message;

                    return model;
                }

                scope.Complete();

                return model;
            }
        }

        private bool CheckOhipExistanceInDB(string number, int practiceId)
        {
            DemographicsHealthCard card = (from hc in context.HealthCards
                                           join dem in context.Demographics on hc.DemographicId equals dem.Id
                                           join pr in context.PatientRecords on dem.PatientRecordId equals pr.Id
                                           where hc.number == number && pr.PracticeId == practiceId
                                           select hc).FirstOrDefault();

            return card != null;
        }

        public List<ValueText1> GetCohortsByPatientRecordId(int patientId, int practiceId)
        {
            List<ValueText1> list = null;

            try
            {
                list = (from pc in context.PatientCohorts
                        join coh in context.Cohorts on pc.CohortId equals coh.Id
                        where pc.PatientId == patientId && coh.practiceId == practiceId
                        select new ValueText1
                        {
                            text = coh.Description,
                            value = pc.Id.ToString()
                        }).ToList();

            }
            catch (Exception ex)
            {
                string Msg = $" ### PatientBLL.GetCohortsById {ex.ExceptionDetails()}";

                _log.Error(Msg);
            }

            return list;
        }
        public VMActionMessage ActiveDeactivePatientAddress(int practiceId, int patientRecordId, int demographicId, int demographicAddressId, int userId, string ipAddress)
        {
            VMActionMessage message = new VMActionMessage { Success = false, Message = "could not update patient's address." };
            if (patientRecordId > 0 && demographicId > 0 && demographicAddressId > 0)
            {
                DemographicsAddress demoAddress = (from da in context.DemographicsAddress
                                                   join demo in context.Demographics on da.DemographicId equals demo.Id
                                                   join pr in context.PatientRecords on demo.PatientRecordId equals pr.Id
                                                   where pr.PracticeId == practiceId && demo.PatientRecordId == patientRecordId && demo.Id == demographicId && da.Id == demographicAddressId
                                                   select da).FirstOrDefault();
                if (demoAddress != null)
                {
                    demoAddress.IsActive = !demoAddress.IsActive;
                    // EF Core: Use Microsoft.EntityFrameworkCore.EntityState instead of System.Data.Entity.EntityState
                    context.Entry(demoAddress).State = EntityState.Modified;
                    int saved = context.SaveChanges(userId, ipAddress);

                    if (saved > 0)
                    {
                        message.Success = saved > 0;
                        message.Message = saved > 0 ? "patient's address has been updated successfully" : message.Message;
                    }
                }
            }

            return message;
        }
        /// appReferrealDoctorId = external doctor id for the referral doctor
        public VMPatientDemo PopulatePatientEdit(int patientRecordId, int practiceId, int appointmentId = 0, int appReferrealDoctorId = 0)
        {
            VMPatientDemo retModel = new VMPatientDemo();
            retModel.IsPatientHasEnroll = false;
            var languages = _languageBll.GetAllLanguagesLookups();

            List<ValueText1> checkedCohorts = GetCohortsByPatientRecordId(patientRecordId, practiceId);
            List<VMLookupItem> retList = null;
            if (checkedCohorts != null && checkedCohorts.Count > 0)
            {
                retList = checkedCohorts.Select(t => new VMLookupItem { Text = t.text, Value = t.value }).ToList();
            }
            retModel.addedCohorts = retList;

            Demographic demographic = context.Demographics.Where(t => t.PatientRecordId == patientRecordId).FirstOrDefault();

            if (demographic == null)
            {
                retModel.message = "There is not patient with supplied ID.";
            }

            retModel.Id = demographic.Id;
            retModel.PracticeId = practiceId;
            retModel.AppointmentId = appointmentId;
            retModel.AppointmentReferralDoctorId = appReferrealDoctorId;
            //add list for table
            retModel.mrnList = GetMRNList(patientRecordId);

            retModel.ProvinceHC_Id = 8; //default ON , then overwriting if there is helth card
            retModel.SalutationId = (int)(demographic.namePrefix);// modelFroDB.SalutationId;
            retModel.FirstName = demographic.firstName;
            retModel.LastName = demographic.lastName;
            retModel.MiddleName = demographic.middleName ?? "";
            DateTime dt_1 = demographic.dateOfBirth ?? DateTime.MaxValue;
            retModel.DateOfBirth = dt_1.Date == DateTime.MaxValue.Date ? "" : dt_1.ToString("MM/dd/yyyy");
            retModel.GenderId = (int)(demographic.gender);
            retModel.InsuranceKindId = (int)demographic.insuranceType;
            retModel.InsuranceCompanyId = demographic.InsuranceCompanyId;
            retModel.PreferedLanguageId = demographic.PreferredLanguageId ?? 1;
            retModel.OfficialLanguageId = demographic.OfficialLanguageId ?? 1;
            retModel.PreferredOfficialLanguageSpecified = demographic.preferredOfficialLanguageSpecified;
            retModel.Languages = languages;
            retModel.ActiveId = (int)demographic.active;
            retModel.activeOld = demographic.active;
            retModel.SIN = demographic.SIN;
            DateTime dt = demographic.StatusDate ?? DateTime.MaxValue;
            retModel.statusDate_ = (dt == DateTime.MaxValue || dt == DateTime.MinValue) ? "-" : dt.ToString("MMM dd, yyyy ");
            retModel.PaymentMethodId = (int)demographic.defaultPaymentMethod;
            retModel.ConsentEmailId = demographic.consentEmail == true ? 0 : 1;
            retModel.Email = demographic.email;
            retModel.hasEmail = demographic.hasEmail;
            retModel.Notes = demographic.notesAboutPatient;
            retModel.PatientRecordId = demographic.PatientRecordId;
            retModel.FederatedId = demographic.FederatedId;
            retModel.chartNumber = demographic.chartNumber;
            retModel.Alias = demographic.aliasLastName;
            retModel.UseAliases = demographic.useAliases;
            retModel.FaxPharmacy = demographic.pharmacyFaxNumber;
            retModel.PhonePharmacy = demographic.pharmacyPhoneNumber;
            retModel.PreferredName = demographic.preferredName;
            retModel.FavoritePharmacy = demographic.FavoritePharmacy;
            retModel.additionalInfo = demographic.info ?? "";
            retModel.uniqueVendorIdSequence = (demographic.uniqueVendorIdSequence == null || demographic.uniqueVendorIdSequence == "") ? "" :
                "Previous Unique ID: " + demographic.uniqueVendorIdSequence;
            retModel.Phar_Address = demographic.Phar_Address;
            retModel.Phar_City = demographic.Phar_City;
            retModel.Phar_ProvinceId = (int)demographic.Phar_Province;
            retModel.Phar_CountryId = (int)demographic.Phar_Country;
            retModel.Phar_PostalCode = demographic.Phar_PostalCode;
            retModel.Phar_Email = demographic.Phar_Email;
            retModel.Phar_AddressTypeId = (int)demographic.Phar_AddressType;


            if (retModel.UseAliases)
            {
                retModel.NameOnModalHeader = retModel.Alias;
            }
            else
            {
                retModel.NameOnModalHeader = (retModel.LastName ?? "") + ", " + (retModel.FirstName ?? "");
            }

            #region Health card
            DemographicsHealthCard helthCard_ = null;
            if (demographic.healthcards != null && demographic.healthcards.Count > 0)
            {
                helthCard_ = demographic.healthcards.OrderByDescending(t => t.Id).FirstOrDefault();
            }
            retModel.Ohip = helthCard_ == null ? "" : helthCard_.number;

            if (helthCard_ != null && helthCard_.number != null)
            {
                #region health card
                retModel.ProvinceHC_Id = (int)helthCard_.provinceCode;
                if (helthCard_.provinceCode == Province.CAON)
                {
                    retModel.OldOhipForUpdate = helthCard_.number;
                }
                else//RMB
                {
                    retModel.RMB = helthCard_.number;
                }
                #endregion
            }

            if (helthCard_ != null && helthCard_.provinceCode == Province.CAON)
            {
                DateTime dt1 = helthCard_.dateIssued ?? DateTime.MaxValue;
                retModel.IssueDate = (dt1 == DateTime.MaxValue || dt1 == DateTime.MinValue) ? "" : dt1.ToString("MM/dd/yyyy");
                DateTime dt2 = helthCard_.expirydate ?? DateTime.MaxValue;
                retModel.ExpiryDate = (dt2 == DateTime.MaxValue || dt2 == DateTime.MinValue) ? "" : dt2.ToString("MM/dd/yyyy");
                DateTime dt3 = helthCard_.lastValidated ?? DateTime.MaxValue;
                retModel.ValidDate = (dt3 == DateTime.MaxValue || dt3 == DateTime.MinValue) ? "" : dt3.ToString("MM/dd/yyyy");
            }
            retModel.Version = helthCard_ == null ? "" : helthCard_.version;
            #endregion

            #region Demographic Address
            List<name_value_id> addressesListRet = GetReturnAddressList_n(demographic.addresses);
            if (addressesListRet != null)
            {
                retModel.addressesList = addressesListRet;
            }
            #endregion

            #region Demographic Contact
            List<name_id_nkx> nxksListRet = CreateListOfPatientContacts(demographic.demographicsContacts);
            if (nxksListRet != null)
            {
                retModel.nxksList = nxksListRet;
            }
            #endregion

            #region phone list
            List<PhoneDataTransfer> phonKList = GetPhoneList_n(demographic.phoneNumbers);
            if (phonKList != null)
            {
                retModel.phoneList = phonKList;
            }
            #endregion

            #region Main Doctor Data
            List<SqlParameter> parms = new List<SqlParameter>
                {
                    new SqlParameter("patienRecordId",patientRecordId)
                };
            var dbPatientMainDocData_ = context.GetData<PatientMainDoctorInfo>("[dbo].[GetMainDoctorInfo]", parms);
            PatientMainDoctorInfo dbPatientMainDocData = dbPatientMainDocData_.FirstOrDefault();
            if (dbPatientMainDocData != null)
            {
                retModel.MainDoctor = dbPatientMainDocData.docFullName;
                retModel.IsMainDoctorNew = "selected";

                retModel.MainDoctorId = dbPatientMainDocData.mainDoctorId;
                retModel.IsPatientHasEnroll = dbPatientMainDocData.EnrolledDocName != "" ? true : false;
                retModel.EnrolledDocName = dbPatientMainDocData.EnrolledDocName;
            }
            #endregion

            DoctorsData refferalDoctor = GetRefferalDocListFromDB(patientRecordId);
            retModel.refDocList = refferalDoctor == null ? null : refferalDoctor.list;

            DoctorsData familyDoctor = GetFamilyDocListFromDB(patientRecordId);
            retModel.familyDocList = familyDoctor == null ? null : familyDoctor.list;

            DoctorsData assocDoctor = GetAssocDocListFromDB(patientRecordId);
            retModel.AssociatedDoctorId = assocDoctor == null ? 0 : assocDoctor.Id;
            retModel.assocDocList = assocDoctor == null ? null : assocDoctor.list;

            return retModel;
        }

        public VMPatientDemo UpdateDemographics(VMPatientDemo retModel, int userid, string ipaddress)
        {
            retModel.message = "";
            retModel.success = false;

            List<ValueText1> checkedCohorts = GetCohortsByPatientRecordId(retModel.PatientRecordId, retModel.PracticeId);
            List<VMLookupItem> retList = null;
            if (checkedCohorts != null && checkedCohorts.Count > 0)
            {
                retList = checkedCohorts.Select(t => new VMLookupItem { Text = t.text, Value = t.value }).ToList();
            }
            retModel.addedCohorts = retList;

            retModel.Ohip = retModel.Ohip ?? "";
            retModel.RMB = retModel.RMB ?? "";
            string ohip_ = retModel.Ohip.Trim();

            if (ohip_ != "")
            {
                retModel.Ohip = ohip_;
            }
            else if (ohip_ == "" && retModel.RMB != "")
            {
                retModel.Ohip = retModel.RMB;
            }
            else
            {
                retModel.Ohip = "";
            }

            if (retModel.Ohip != "" && retModel.OldOhipForUpdate != retModel.Ohip && retModel.skipOHIPCheck == false)
            {
                bool isOhipExistsInDB = CheckOhipExistanceInDB(retModel.Ohip, retModel.PracticeId);
                if (isOhipExistsInDB)
                {
                    retModel.message = "There is already patient in DB with ohip number: " + ohip_ + "  ! ";
                    return retModel;
                }
            }


            using (TransactionScope scope = new TransactionScope())
            {
                try
                {
                    var demographic_int = UpdateDemographics_n(retModel, userid, ipaddress);

                    bool hc_bool = UpdateHealthCard_n(retModel, userid, ipaddress);

                    bool famDoc_bool = UpdateFamDoctor_n(retModel, demographic_int, userid, ipaddress);

                    bool main_bool = UpdateMainDoctor_n(retModel, demographic_int, userid, ipaddress);
                    if (main_bool)
                    {
                        retModel.IsMainDoctorNew = "selected";
                    }

                    bool assos_bool = UpdateAssosiatedDoctor_n(retModel, demographic_int, userid, ipaddress);

                    scope.Complete();

                    retModel.message = "Data saved in to database !";
                    retModel.success = true;

                    return retModel;
                }
                catch (Exception ex)
                {

                    string Msg = $" ### NewPatientEdit  TransactionScope scope 1 {ex.ExceptionDetails()}";
                    _log.Error(Msg);

                    retModel.message = "There was problem to update database !";

                    return retModel;
                }
            }
        }

        public VMExternalDoctorReportContact GetPatientReportContact(VMExtDocRepContactRequest request, int userId, string ipAddress)
        {
            var externalDoctorId = request.ExternalDoctorId;
            var appointmentId = request.AppointmentId;
            var patientId = request.PatientId;
            var patientName = request.PatientName;
            var docType = request.DocType;
            var isEditPatient = request.IsEditPatient;
            var currentDate = System.DateTime.Now;
            var exDocContact = new VMExternalDoctorReportContact();
            var exDoctor = _externalDoctorBll.GetExternalDoctor(externalDoctorId);
            var hasAppointment = false;
            int externalDocAddressId = 0;
            int externalDocPhoneId = 0;
            Appointment app = null;

            if (appointmentId > 0)
            {
                app = context.Appointments.Find(appointmentId);
                if (app != null)
                {
                    hasAppointment = true;
                    if (app.ReferralDoctorAddressId > 0)
                    {
                        externalDocAddressId = (int)app.ReferralDoctorAddressId;
                    }

                    if (app.ReferralDoctorPhoneNumberId > 0)
                    {
                        externalDocPhoneId = (int)app.ReferralDoctorPhoneNumberId;
                    }
                }

            }

            if (patientId > 0 && externalDocAddressId == 0 && externalDocPhoneId == 0)
            {
                //check to see if there is one already linked to the patient
                var patientLoc = GetActivePatientLocation(patientId, externalDoctorId);
                if (patientLoc != null)
                {
                    var externalDoctorLocationId = patientLoc.ExternalDoctorLocationId;
                    externalDocAddressId = patientLoc.ExternalDoctorAddressId;
                    externalDocPhoneId = patientLoc.ExternalDoctorPhoneNumberId;

                    if (hasAppointment && docType == DocType.Referral && app.referralDoctorId == externalDoctorId)
                    {
                        var reportContact = new VMSetExDocReportContact();
                        reportContact.ExternalDoctorLocationId = externalDoctorLocationId;
                        reportContact.ExternalDoctorId = externalDoctorId;
                        reportContact.ExternalDoctorAddressId = externalDocAddressId;
                        reportContact.ExternalDoctorPhoneId = externalDocPhoneId;
                        reportContact.PatientId = patientId;
                        reportContact.DoctorType = docType;
                        reportContact.AppointmentId = appointmentId;

                        UpdatePatientAppointmentsLocations(reportContact, userId, ipAddress);
                        exDocContact.RefreshAppointment = true;

                    }
                }
            }
            else if (externalDocAddressId > 0 && externalDocPhoneId > 0)
            {
                var docLocation = exDoctor.Addresses.SelectMany(x => x.PhoneNumbers
                .Where(p => p.ExternalDoctorLocationId > 0
                && p.ExternalDoctorAddressId == externalDocAddressId
                && p.Id == externalDocPhoneId)
                .Select(s => s)).FirstOrDefault();

                if (docLocation != null)
                {
                    var externalDoctorLocationId = docLocation.ExternalDoctorLocationId;
                    var reportContact = new VMSetExDocReportContact();
                    reportContact.ExternalDoctorLocationId = externalDoctorLocationId;
                    reportContact.ExternalDoctorId = externalDoctorId;
                    reportContact.ExternalDoctorAddressId = externalDocAddressId;
                    reportContact.ExternalDoctorPhoneId = externalDocPhoneId;
                    reportContact.PatientId = patientId;
                    reportContact.DoctorType = docType;
                    reportContact.AppointmentId = 0; // dont need to update the appointment

                    UpdatePatientReportContact(reportContact, userId, ipAddress);
                }
            }

            exDocContact.DoctorType = docType;
            exDocContact.AppointmentId = appointmentId;
            exDocContact.PatientId = patientId;
            exDocContact.PatientName = patientName;
            exDocContact.ExternalDoctorAddressId = externalDocAddressId;
            exDocContact.ExternalDoctorPhoneId = externalDocPhoneId;
            exDocContact.ExternalDoctor = exDoctor;
            exDocContact.IsEditPatient = isEditPatient;

            return exDocContact;
        }

        public void UpdatePatientReportContact(VMSetExDocReportContact patientRepContact, int userId, string ipAddress)
        {
            int appointmentId = patientRepContact.AppointmentId;
            int patientId = patientRepContact.PatientId;
            var externalDoctorId = patientRepContact.ExternalDoctorId;
            int externalDoctorAddressId = patientRepContact.ExternalDoctorAddressId;
            int externalDoctorPhoneId = patientRepContact.ExternalDoctorPhoneId;
            int externalDoctorLocationId = patientRepContact.ExternalDoctorLocationId;
            var isEditPatient = patientRepContact.IsEditPatient;
            var docType = patientRepContact.DoctorType;
            var currentDate = System.DateTime.Now;
            var patientLocations = GetPatientLocations(patientId, externalDoctorId);

            UpdatePatientAppointmentsLocations(patientRepContact, userId, ipAddress);

            //check if this address is associated with the patient
            var patientLocation = context.PatientLocations
            .FirstOrDefault(x => x.ExternalDoctorLocationId == externalDoctorLocationId && x.PatientRecordId == patientId);

            if (patientLocation == null)
            {
                patientLocation = new PatientLocation();
                patientLocation.ExternalDoctorLocationId = externalDoctorLocationId;
                patientLocation.PatientRecordId = patientId;
                patientLocation.LastModifiedByUserId = userId;

                context.PatientLocations.Add(patientLocation);
                context.SaveChanges(userId, ipAddress);
            }
            else
            {
                if (patientLocation.IsActive == false)
                {
                    patientLocation.IsActive = true;
                    patientLocation.DateLastModified = currentDate;
                    patientLocation.LastModifiedByUserId = userId;

                    // EF Core: Use Microsoft.EntityFrameworkCore.EntityState instead of System.Data.Entity.EntityState
                    context.Entry(patientLocation).State = EntityState.Modified;
                    context.SaveChanges(userId, ipAddress);
                }

            }

            //remove the currently selected doctorlocation from the patient list
            patientLocations = patientLocations.Where(x => x.ExternalDoctorLocationId != externalDoctorLocationId).ToList();
            foreach (var patLoc in patientLocations)
            {
                var dbPatLoc = context.PatientLocations.Find(patLoc.PatientLocationId);
                if (dbPatLoc != null)
                {
                    //deactive because we only need one active for this doctor and patient
                    dbPatLoc.IsActive = false;
                    dbPatLoc.DateLastModified = currentDate;
                    dbPatLoc.LastModifiedByUserId = userId;

                    // EF Core: Use Microsoft.EntityFrameworkCore.EntityState instead of System.Data.Entity.EntityState
                    context.Entry(dbPatLoc).State = EntityState.Modified;
                    context.SaveChanges(userId, ipAddress);
                }
            }
        }

        public void ResetPatientReportContact(int appointmentId, bool isPhone, int userId, string ipAddress)
        {
            var ap = context.Appointments.Find(appointmentId);
            ap.ReferralDoctorPhoneNumberId = null;
            ap.ReferralDoctorAddressId = null;

            // EF Core: Use Microsoft.EntityFrameworkCore.EntityState instead of System.Data.Entity.EntityState
            context.Entry(ap).State = EntityState.Modified;
            context.SaveChanges(userId, ipAddress);
        }

        public void UpdatePatientAppointmentsLocations(VMSetExDocReportContact exDocRepContact, int userId, string ipAddress)
        {
            Appointment ap = null;
            int appointmentId = exDocRepContact.AppointmentId;
            int patientId = exDocRepContact.PatientId;
            var externalDoctorId = exDocRepContact.ExternalDoctorId;
            int externalDoctorAddressId = exDocRepContact.ExternalDoctorAddressId;
            int externalDoctorPhoneId = exDocRepContact.ExternalDoctorPhoneId;
            var docType = exDocRepContact.DoctorType;
            var currentDate = System.DateTime.Now;
            var hasAppointment = false;

            if (appointmentId > 0)
            {
                ap = context.Appointments.Find(appointmentId);
                if (ap != null && ap.referralDoctorId == externalDoctorId && docType == DocType.Referral)
                {
                    hasAppointment = true;
                }
            }

            if (hasAppointment)
            {
                //if they have any other appointments on the same day and update them all with the new setting
                if (externalDoctorAddressId > 0 && externalDoctorPhoneId > 0)
                {
                    var appDate = ap.appointmentTime;
                    var startDate = appDate.Date;
                    var endDate = startDate.AddDays(1);

                    var patientApps = context.Appointments
                        .Where(x => x.appointmentTime >= startDate
                        && x.appointmentTime < endDate
                        && x.referralDoctorId == externalDoctorId
                        && x.PatientRecordId == patientId
                        && x.IsActive == true)
                        .ToList();

                    foreach (var patientApp in patientApps)
                    {
                        patientApp.LastModified = currentDate;
                        patientApp.ReferralDoctorAddressId = externalDoctorAddressId;
                        patientApp.ReferralDoctorPhoneNumberId = externalDoctorPhoneId;

                        // EF Core: Use Microsoft.EntityFrameworkCore.EntityState instead of System.Data.Entity.EntityState
                        context.Entry(patientApp).State = EntityState.Modified;
                        context.SaveChanges(userId, ipAddress);
                    }

                }
            }
        }
        private List<VMPatientLocation> GetPatientLocations(int patientId, int externalDoctorId)
        {
            var locations = new List<VMPatientLocation>();

            if (patientId > 0 && externalDoctorId > 0)
            {
                var parameters = new List<SqlParameter> {
                new SqlParameter("externalDoctorId", externalDoctorId),
                new SqlParameter("patientId", patientId)
                };
                var dbLocations = context.GetData<SP_PatientLocation>("[dbo].[GetPatientLocations]", parameters).ToList();

                foreach (var dbLocation in dbLocations)
                {
                    var location = LoadPatientLocation(dbLocation);
                    locations.Add(location);
                }
            }
            return locations;
        }

        public VMPatientLocation GetActivePatientLocation(int patientId, int externalDoctorId)
        {
            var dbPatientLocations = GetPatientLocations(patientId, externalDoctorId);
            dbPatientLocations = dbPatientLocations
                .Where(x => x.FaxNumber != null
                && x.FaxNumber != ""
                && x.IsActivePatientLocation
                && x.IsActiveAddress
                && x.IsActiveLocation
                && x.IsActivePhone).ToList();

            var location = dbPatientLocations.FirstOrDefault();

            return location;
        }

        private int UpdateDemographics_n(VMPatientDemo demographicsViewModel, int userid, string ipaddress)
        {
            Demographic demographic = context.Demographics.Select(t => t).
                Where(t => t.PatientRecordId == demographicsViewModel.PatientRecordId).FirstOrDefault();
            if (demographic != null)
            {
                demographic.active = (Active)demographicsViewModel.ActiveId;
                demographic.SIN = demographicsViewModel.SIN;
                demographic.consentEmail = demographicsViewModel.ConsentEmailId == 1 ? false : true;
                if (demographicsViewModel.DateOfBirth != null && demographicsViewModel.DateOfBirth != "")
                {
                    demographic.dateOfBirth = Convert.ToDateTime(demographicsViewModel.DateOfBirth);
                }
                else
                {
                    demographic.dateOfBirth = null;
                }
                demographic.hasEmail = demographicsViewModel.hasEmail;
                demographic.email = demographicsViewModel.hasEmail ? demographicsViewModel.Email : string.Empty;
                demographic.firstName = demographicsViewModel.FirstName;

                if (demographicsViewModel.activeOld != (Active)demographicsViewModel.ActiveId)
                {
                    demographic.StatusDate = DateTime.Now;
                }

                demographic.gender = (Gender)demographicsViewModel.GenderId;
                demographic.insuranceType = (InsuranceKind)demographicsViewModel.InsuranceKindId;
                //int insuranceCompId = 0;
                //int.TryParse(demographicsViewModel.InsuranceCompanyId, out insuranceCompId);
                demographic.InsuranceCompanyId = demographicsViewModel.InsuranceCompanyId;
                demographic.lastName = demographicsViewModel.LastName;
                demographic.middleName = demographicsViewModel.MiddleName;
                demographic.namePrefix = (Salutation)demographicsViewModel.SalutationId;
                demographic.notesAboutPatient = demographicsViewModel.Notes;
                demographic.defaultPaymentMethod = (PaymentMethod)demographicsViewModel.PaymentMethodId;
                demographic.preferredOfficialLanguageSpecified = demographicsViewModel.PreferredOfficialLanguageSpecified;
                demographic.OfficialLanguageId = demographicsViewModel.OfficialLanguageId;
                demographic.PreferredLanguageId = demographicsViewModel.PreferedLanguageId;
                demographic.HospitalCode = context.Hospitals.Where(t => t.Id == demographicsViewModel.HospitalId).
                            Select(tt => tt.code).FirstOrDefault();
                demographic.preferredSpokenLanguage = Enum.GetName(typeof(Language), demographicsViewModel.PreferedLanguageId);
                demographic.FederatedId = demographicsViewModel.FederatedId;
                demographic.aliasLastName = demographicsViewModel.Alias;
                demographic.chartNumber = demographicsViewModel.chartNumber;
                demographic.useAliases = demographicsViewModel.UseAliases;
                demographic.pharmacyFaxNumber = demographicsViewModel.FaxPharmacy;
                demographic.pharmacyPhoneNumber = demographicsViewModel.PhonePharmacy;
                demographic.preferredName = demographicsViewModel.PreferredName;
                demographic.FavoritePharmacy = demographicsViewModel.FavoritePharmacy;

                demographic.Phar_Address = demographicsViewModel.Phar_Address;
                demographic.Phar_City = demographicsViewModel.Phar_City;
                demographic.Phar_Country = (Country)demographicsViewModel.Phar_CountryId;
                demographic.Phar_Email = demographicsViewModel.Phar_Email;
                demographic.Phar_PostalCode = demographicsViewModel.Phar_PostalCode;
                demographic.Phar_Province = (Province)demographicsViewModel.Phar_ProvinceId;
                demographic.Phar_AddressType = (AddressTypes)demographicsViewModel.Phar_AddressTypeId;


                context.Entry(demographic).State = EntityState.Modified;
                try
                {
                    context.SaveChanges(userid, ipaddress);

                    return demographic.Id;
                }
                catch (Exception ex)
                {
                    string Msg = $" ### UpdateDemographics {ex.ExceptionDetails()}";

                    _log.Error(Msg);

                    demographicsViewModel.message = "There was a problem. Contact Administrator! ";
                    return 0;
                }
            }
            else
            {
                return 0;
            }
        }

        private bool UpdateHealthCard_n(VMPatientDemo demographicsViewModel, int userid, string ipaddress)
        {
            demographicsViewModel.message = "";
            var helthCard = context.HealthCards.Where(w => w.Demographic.PatientRecordId == demographicsViewModel.PatientRecordId).
                OrderByDescending(o => o.Id).FirstOrDefault();

            demographicsViewModel.Ohip = demographicsViewModel.Ohip ?? "";
            demographicsViewModel.RMB = demographicsViewModel.RMB ?? "";

            string ohip = demographicsViewModel.Ohip.Trim();

            if (helthCard != null)
            {
                if (!demographicsViewModel.skipOHIPCheck)
                {
                    if (ohip != "")
                    {
                        helthCard.number = ohip;

                    }
                    else if (ohip == "" && demographicsViewModel.RMB != "")
                    {
                        helthCard.number = demographicsViewModel.RMB;
                    }
                }

                if (demographicsViewModel.skipOHIPCheck && ohip == "")
                {
                    helthCard.number = string.Empty;
                }

                if (!string.IsNullOrWhiteSpace(demographicsViewModel.IssueDate))
                {
                    helthCard.dateIssued = Convert.ToDateTime(demographicsViewModel.IssueDate);
                }
                if (!string.IsNullOrWhiteSpace(demographicsViewModel.ValidDate))
                {
                    helthCard.lastValidated = Convert.ToDateTime(demographicsViewModel.ValidDate);
                }
                if (!string.IsNullOrWhiteSpace(demographicsViewModel.ExpiryDate))
                {
                    helthCard.expirydate = Convert.ToDateTime(demographicsViewModel.ExpiryDate);
                }
                helthCard.provinceCode = (Province)demographicsViewModel.ProvinceHC_Id;
                helthCard.version = demographicsViewModel.Version;

                context.Entry(helthCard).State = EntityState.Modified;
                try
                {
                    context.SaveChanges(userid, ipaddress);
                    return true;
                }
                catch (Exception ex)
                {
                    string Msg = $" ### UpdateHealthCard_n(DemographicsViewModel demographicsViewModel) {ex.ExceptionDetails()}";

                    _log.Error(Msg);

                    demographicsViewModel.message = "There was a problem. Contact Administrator! ";
                    return false;
                }
            }
            else
            {
                var ptntHelthCards = context.HealthCards.Where(t => t.number == ohip).FirstOrDefault();
                int demographicsId = (from d in context.Demographics
                                      join p in context.PatientRecords on d.PatientRecordId equals p.Id
                                      where p.Id == demographicsViewModel.PatientRecordId
                                      select d.Id).FirstOrDefault();
                if (demographicsId == 0)
                { return false; }

                DemographicsHealthCard ohipCard = new DemographicsHealthCard();
                ohipCard.DemographicId = demographicsId;
                if (!demographicsViewModel.skipOHIPCheck)
                {
                    ohipCard.number = demographicsViewModel.Ohip;


                    if (demographicsViewModel.ProvinceHC_Id == 8)
                    {
                        if (ValidationMethods.IsValidDate(demographicsViewModel.IssueDate))
                        {
                            ohipCard.dateIssued = Convert.ToDateTime(demographicsViewModel.IssueDate);
                        }
                        if (ValidationMethods.IsValidDate(demographicsViewModel.ExpiryDate))
                        {
                            ohipCard.expirydate = Convert.ToDateTime(demographicsViewModel.ExpiryDate);
                        }
                        if (ValidationMethods.IsValidDate(demographicsViewModel.ValidDate))
                        {
                            ohipCard.lastValidated = Convert.ToDateTime(demographicsViewModel.ValidDate);
                        }
                        ohipCard.version = demographicsViewModel.Version;
                        ohipCard.provinceCode = (Province)(demographicsViewModel.ProvinceHC_Id);
                    }
                    else
                    {
                        ohipCard.number = demographicsViewModel.RMB;
                        ohipCard.provinceCode = (Province)(demographicsViewModel.ProvinceHC_Id);
                    }
                }

                context.Entry(ohipCard).State = EntityState.Added;

                try
                {
                    context.SaveChanges(userid, ipaddress);
                    return true;
                }
                catch (Exception ex)
                {
                    string Msg = $" ### UpdateHealthCard_n(DemographicsViewModel demographicsViewModel new card) {ex.ExceptionDetails()}";
                    _log.Error(Msg);

                    return false;
                }
            }
        }

        private bool UpdateFamDoctor_n(VMPatientDemo demographicsViewModel, int demographicId, int userid, string ipaddress)
        {
            if (string.IsNullOrWhiteSpace(demographicsViewModel.FamDoctor))
            {
                return false;
            }

            int famDocHidInt = demographicsViewModel.FamDoctorId;
            if (famDocHidInt == 0)
            {
                return false;
            }

            //do not let double same doctor
            #region double same doctor
            if (demographicId != 0)
            {
                DemographicsFamilyDoctor famDocChk = (from d in context.Demographics
                                                      join pr in context.PatientRecords on d.PatientRecordId equals pr.Id
                                                      join dfm in context.DemographicsFamilyDoctors on d.Id equals dfm.DemographicId
                                                      where dfm.DemographicId == demographicId
                                                      && dfm.ExternalDoctorId == famDocHidInt
                                                      && pr.PracticeId == demographicsViewModel.PracticeId
                                                      select dfm).FirstOrDefault();

                if (famDocChk != null)
                {
                    if (famDocChk.IsRemoved == false)
                    {
                        return false;
                    }
                    else
                    {
                        return MakeDemFamilyDoctorUnremoved(demographicsViewModel, demographicId, userid, ipaddress, famDocChk);
                    }
                }
                else
                {
                    return RegisterNewDemFamilyDoctor(demographicsViewModel, demographicId, userid, ipaddress, famDocChk, famDocHidInt);
                }
            }
            else
            {
                return false;
            }
            #endregion
        }

        private bool UpdateMainDoctor_n(VMPatientDemo demographicsViewModel, int demographicId, int userid, string ipaddress)
        {
            //main doctor practiceDoctorId
            int MRP_Id = demographicsViewModel.MainDoctorId;
            if (MRP_Id == 0)
            {
                return false;
            }

            List<DemographicsMainResponsiblePhysician> mainDocList = (from dmrp in context.DemographicsMainResponsiblePhysicians
                                                                      join pr in context.PracticeDoctors on dmrp.PracticeDoctorId equals pr.Id
                                                                      where dmrp.DemographicId == demographicId
                                                                      && pr.PracticeId == demographicsViewModel.PracticeId
                                                                      orderby dmrp.Id
                                                                      select dmrp).ToList();

            DemographicsMainResponsiblePhysician mainDoc = null;

            if (mainDocList != null && mainDocList.Count > 0)
            {
                mainDoc = mainDocList.Where(tt => tt.DemographicId == demographicId && tt.PracticeDoctorId == MRP_Id).
                                        Select(t => t).OrderByDescending(t => t.Id).FirstOrDefault();
            }

            DemographicsMainResponsiblePhysician mn_Doctor = null;
            if (mainDoc != null)
            {
                mainDocList.Remove(mainDoc);
                if (mainDocList.Count > 0)
                {
                    foreach (var item in mainDocList)
                    {
                        item.IsActive = false;
                        context.Entry(item).State = EntityState.Modified;
                    }
                }

                mainDoc.IsActive = true;
                context.Entry(mainDoc).State = EntityState.Modified;
            }
            else
            {
                if (mainDocList.Count > 0)
                {
                    foreach (var item in mainDocList)
                    {
                        item.IsActive = false;
                        context.Entry(item).State = EntityState.Modified;
                    }
                }

                mn_Doctor = new DemographicsMainResponsiblePhysician();
                mn_Doctor.DemographicId = demographicId;
                mn_Doctor.IsActive = true;
                mn_Doctor.ExternalDoctorId = context.PracticeDoctors.Where(t => t.Id == MRP_Id).
                                            Select(tt => tt.ExternalDoctorId).FirstOrDefault();
                mn_Doctor.PracticeDoctorId = MRP_Id;

                context.Entry(mn_Doctor).State = EntityState.Added;

            }

            try
            {
                context.SaveChanges(userid, ipaddress);
                return true;
            }
            catch (Exception ex)
            {
                string Msg = $" ### UpdateMainDoctor_n {ex.ExceptionDetails()}";

                _log.Error(Msg);

                demographicsViewModel.message = "There was a problem. Contact Administrator! ";
                return false;
            }
        }

        private bool UpdateAssosiatedDoctor_n(VMPatientDemo demographicsViewModel, int demographicId, int userid, string ipaddress)
        {
            if (string.IsNullOrWhiteSpace(demographicsViewModel.AssociatedDoctor))
            {
                return false;
            }

            int assosDocHidInt = demographicsViewModel.AssociatedDoctorId;
            if (assosDocHidInt == 0)
            {
                return false;
            }

            //do not let double same doctor
            #region double same doctor
            if (demographicId != 0)
            {
                DemographicsAssociatedDoctor assoccDocChk = (from d in context.Demographics
                                                             join pr in context.PatientRecords on d.PatientRecordId equals pr.Id
                                                             join dfm in context.DemographicsAssociatedDoctors on d.Id equals dfm.DemographicId
                                                             where dfm.DemographicId == demographicId
                                                             && dfm.ExternalDoctorId == assosDocHidInt
                                                             && pr.PracticeId == demographicsViewModel.PracticeId
                                                             select dfm).FirstOrDefault();

                if (assoccDocChk != null)
                {
                    if (assoccDocChk.IsRemoved == false)
                    {
                        return false;
                    }
                    else
                    {
                        return MakeDemAssoccDoctorUnremoved(demographicsViewModel, demographicId, userid, ipaddress, assoccDocChk);
                    }
                }
                else
                {
                    return RegisterNewDemAssoccDoctor(demographicsViewModel, demographicId, userid, ipaddress, assoccDocChk, assosDocHidInt);
                }
            }
            else
            {
                return false;
            }
            #endregion
        }

        private bool RegisterNewDemAssoccDoctor(VMPatientDemo demographicsViewModel, int demographicId, int userid, string ipaddress, DemographicsAssociatedDoctor assoccDocChk, int assosDocHidInt)
        {
            List<DemographicsAssociatedDoctor> doctors = (from d in context.Demographics
                                                          join pr in context.PatientRecords on d.PatientRecordId equals pr.Id
                                                          join dfm in context.DemographicsAssociatedDoctors on d.Id equals dfm.DemographicId
                                                          where dfm.DemographicId == demographicId
                                                          && pr.PracticeId == demographicsViewModel.PracticeId
                                                          select dfm).ToList();


            if (doctors != null && doctors.Count > 0)
            {
                foreach (var item in doctors)
                {
                    item.IsActive = false;
                    context.Entry(item).State = EntityState.Modified;
                }
            }

            DemographicsAssociatedDoctor assDoctor = new DemographicsAssociatedDoctor();
            assDoctor.DemographicId = demographicId;
            assDoctor.ExternalDoctorId = assosDocHidInt;
            assDoctor.IsActive = true;
            assDoctor.CreatedDateTime = DateTime.Now;

            context.Entry(assDoctor).State = EntityState.Added;
            try
            {
                context.SaveChanges(userid, ipaddress);
                return true;
            }
            catch (Exception ex)
            {
                string Msg = $" ### RegisterNewDemAssoccDoctor {ex.ExceptionDetails()}";
                _log.Error(Msg);

                demographicsViewModel.message = "There was a problem. Contact Administrator! ";
                return false;
            }
        }

        private bool MakeDemAssoccDoctorUnremoved(VMPatientDemo demographicsViewModel, int demographicId, int userid, string ipaddress, DemographicsAssociatedDoctor assoccDocChk)
        {
            List<DemographicsAssociatedDoctor> doctorsR = (from d in context.Demographics
                                                           join pr in context.PatientRecords on d.PatientRecordId equals pr.Id
                                                           join dfm in context.DemographicsAssociatedDoctors on d.Id equals dfm.DemographicId
                                                           where dfm.DemographicId == demographicId
                                                           && pr.PracticeId == demographicsViewModel.PracticeId
                                                           select dfm).ToList();



            if (doctorsR != null && doctorsR.Count > 0)
            {
                foreach (var item in doctorsR)
                {
                    item.IsActive = false;
                    context.Entry(item).State = EntityState.Modified;
                }
            }


            assoccDocChk.IsRemoved = false;
            assoccDocChk.IsActive = true;
            context.Entry(assoccDocChk).State = EntityState.Modified;

            try
            {
                context.SaveChanges(userid, ipaddress);
                return true;
            }
            catch (Exception ex)
            {
                string Msg = $" ### MakeDemAssoccDoctorUnremoved! {ex.ExceptionDetails()}";
                _log.Error(Msg);

                demographicsViewModel.message = "There was a problem. Contact Administrator! ";
                return false;
            }
        }

        private bool RegisterNewDemFamilyDoctor(VMPatientDemo demographicsViewModel, int demographicId, int userid, string ipaddress, DemographicsFamilyDoctor famDocChk, int famDocHidInt)
        {
            List<DemographicsFamilyDoctor> doctors = (from d in context.Demographics
                                                      join pr in context.PatientRecords on d.PatientRecordId equals pr.Id
                                                      join dfm in context.DemographicsFamilyDoctors on d.Id equals dfm.DemographicId
                                                      where dfm.DemographicId == demographicId
                                                      && pr.PracticeId == demographicsViewModel.PracticeId
                                                      select dfm).ToList();


            if (doctors != null && doctors.Count > 0)
            {
                foreach (var item in doctors)
                {
                    item.IsActive = false;
                    context.Entry(item).State = EntityState.Modified;
                }
            }

            DemographicsFamilyDoctor famDoctor = new DemographicsFamilyDoctor();
            famDoctor.DemographicId = demographicId;
            famDoctor.ExternalDoctorId = famDocHidInt;
            famDoctor.IsActive = true;
            famDoctor.CreatedDate = DateTime.Now;

            context.Entry(famDoctor).State = EntityState.Added;
            try
            {
                context.SaveChanges(userid, ipaddress);
                return true;
            }
            catch (Exception ex)
            {
                string Msg = $" ### RegisterNewDemFamilyDoctor {ex.ExceptionDetails()}";
                _log.Error(Msg);

                demographicsViewModel.message = "There was a problem. Contact Administrator! ";
                return false;
            }
        }

        private bool MakeDemFamilyDoctorUnremoved(VMPatientDemo demographicsViewModel, int demographicId, int userid, string ipaddress, DemographicsFamilyDoctor famDocChk)
        {
            List<DemographicsFamilyDoctor> doctorsR = (from d in context.Demographics
                                                       join pr in context.PatientRecords on d.PatientRecordId equals pr.Id
                                                       join dfm in context.DemographicsFamilyDoctors on d.Id equals dfm.DemographicId
                                                       where dfm.DemographicId == demographicId
                                                       && pr.PracticeId == demographicsViewModel.PracticeId
                                                       select dfm).ToList();



            if (doctorsR != null && doctorsR.Count > 0)
            {
                foreach (var item in doctorsR)
                {
                    item.IsActive = false;
                    context.Entry(item).State = EntityState.Modified;
                }
            }


            famDocChk.IsRemoved = false;
            famDocChk.IsActive = true;
            context.Entry(famDocChk).State = EntityState.Modified;

            try
            {
                context.SaveChanges(userid, ipaddress);
                return true;
            }
            catch (Exception ex)
            {
                string Msg = $" ### MakeDemFamilyDoctorUnremoved! {ex.ExceptionDetails()}";
                _log.Error(Msg);

                demographicsViewModel.message = "There was a problem. Contact Administrator! ";
                return false;
            }
        }

        private VMPatientLocation LoadPatientLocation(SP_PatientLocation dbLocation)
        {
            var location = new VMPatientLocation();
            location.PatientId = dbLocation.PatientId;
            location.PatientLocationId = dbLocation.PatientLocationId;
            location.ExternalDoctorLocationId = dbLocation.ExternalDoctorLocationId;
            location.ExternalDoctorAddressId = dbLocation.ExternalDoctorAddressId;
            location.ExternalDoctorId = dbLocation.ExternalDoctorId;
            location.AddressName = dbLocation.AddressName;
            location.AddressLine1 = dbLocation.AddressLine1;
            location.AddressLine2 = dbLocation.AddressLine2;
            location.City = dbLocation.City;
            location.PostalCode = dbLocation.PostalCode;
            location.Province = dbLocation.Province;
            location.Country = dbLocation.Country;
            location.AddressType = dbLocation.AddressType;
            location.IsActivePatientLocation = dbLocation.IsActivePatientLocation;
            location.IsActiveAddress = dbLocation.IsActiveAddress;
            location.IsActiveLocation = dbLocation.IsActiveLocation;

            if (!String.IsNullOrWhiteSpace(dbLocation.PhoneNumber))
            {
                location.PhoneNumber = Cerebrum.BLL.Utility.PhoneUtils.FormatPhone(dbLocation.PhoneNumber);
            }

            if (!String.IsNullOrWhiteSpace(dbLocation.FaxNumber))
            {
                location.FaxNumber = Cerebrum.BLL.Utility.PhoneUtils.FormatPhone(dbLocation.FaxNumber);
            }

            location.ExternalDoctorPhoneNumberId = dbLocation.ExternalDoctorPhoneNumberId;
            location.PhoneNumberExtension = dbLocation.PhoneNumberExtension;
            location.TypeOfPhoneNumber = dbLocation.TypeOfPhoneNumber;
            location.IsActivePhone = dbLocation.IsActivePhone;

            return location;
        }

        public VMPatientDemo GetDemographicsFromDB(int patientRecordId)
        {
            //if (patientRecordId > 0)
            //{
            //    List<SqlParameter> parms = new List<SqlParameter>
            //    {
            //        new SqlParameter("patRecordId",patientRecordId)
            //    };

            //    var dbPatientData = context.GetData<Demographic>("[dbo].[GetPatientDemographics]", parms);

            //}

            Demographic demographic = context.Demographics.Select(t => t).
                Where(t => t.PatientRecordId == patientRecordId).FirstOrDefault();
            if (demographic == null)
            {
                return null;
            }
            VMPatientDemo model = new VMPatientDemo();
            model.Id = demographic.Id;
            model.PatientRecordId = demographic.PatientRecordId;

            model.ActiveId = (int)demographic.active;
            model.activeOld = demographic.active;
            model.ConsentEmailId = demographic.consentEmail == true ? 0 : 1;
            DateTime dt_1 = demographic.dateOfBirth ?? DateTime.MaxValue;
            model.DateOfBirth = dt_1.Date == DateTime.MaxValue.Date ? "" : dt_1.ToString("MM/dd/yyyy");
            DateTime dt = demographic.StatusDate ?? DateTime.MaxValue;
            model.statusDate_ = (dt == DateTime.MaxValue || dt == DateTime.MinValue) ? "-" : dt.ToString("MMM dd, yyyy ");
            model.StatusDate = demographic.StatusDate;
            model.Email = demographic.email;
            model.FirstName = demographic.firstName;
            model.LastName = demographic.lastName;
            model.MiddleName = demographic.middleName;
            model.GenderId = (int)demographic.gender;
            model.InsuranceKindId = (int)demographic.insuranceType;
            model.InsuranceCompanyId = demographic.InsuranceCompanyId;
            model.SalutationId = (int)demographic.namePrefix;
            model.Notes = demographic.notesAboutPatient;
            model.PaymentMethodId = (int)demographic.defaultPaymentMethod;
            model.chartNumber = demographic.chartNumber;
            model.PreferredOfficialLanguageSpecified = demographic.preferredOfficialLanguageSpecified;
            model.SelectedHospitalId = demographic.HospitalCode;
            model.OfficialLanguage = demographic.preferredSpokenLanguage;
            model.PreferedLanguage = GetLanguage(demographic);
            model.PreferedLanguageId = demographic.PreferredLanguageId ?? 1;
            model.OfficialLanguageId = demographic.OfficialLanguageId ?? 1;
            model.UseAliases = demographic.useAliases;
            model.aliasLastName = demographic.aliasLastName;
            model.FederatedId = demographic.FederatedId;
            model.FaxPharmacy = demographic.pharmacyFaxNumber;
            model.PhonePharmacy = demographic.pharmacyPhoneNumber;
            model.PreferredName = demographic.preferredName;
            model.SIN = demographic.SIN;
            model.FavoritePharmacy = demographic.FavoritePharmacy;
            model.personStatusCode = demographic.personStatusCode.ToString();
            model.personStatusDate = demographic.personStatusDate;
            model.personStatusDateSpecified = demographic.personStatusDateSpecified;
            model.additionalInfo = demographic.info;
            model.uniqueVendorIdSequence = demographic.uniqueVendorIdSequence;
            model.Phar_Address = demographic.Phar_Address;
            model.Phar_City = demographic.Phar_City;
            model.Phar_ProvinceId = (int)demographic.Phar_Province;
            model.Phar_CountryId = (int)demographic.Phar_Country;
            model.Phar_PostalCode = demographic.Phar_PostalCode;
            model.Phar_Email = demographic.Phar_Email;
            model.Phar_AddressTypeId = (int)demographic.Phar_AddressType;

            return model;
        }

        private Language GetLanguage(Demographic demographic)
        {
            Language retLanguage = Language.ENG;
            try
            {
                retLanguage = (Language)Enum.Parse(typeof(Language), demographic.preferredSpokenLanguage);
            }
            catch (Exception)
            {
            }

            return retLanguage;
        }

        private List<ValueTextBool> GetMRNList(int patientRecordId)
        {
            List<ValueTextBool> retList = new List<ValueTextBool>();
            List<ValueTextBool> list = (from hos in context.Hospitals
                                            //join hos in uofw.Hospitals on pmrn.HospitalId equals hos.Id
                                            //where pmrn.PatientRecordId == patientRecordId
                                        select new ValueTextBool
                                        {
                                            text = hos.name,
                                            Id = hos.Id,
                                            value = context.PatientMRNs.Where(t => t.PatientRecordId == patientRecordId
                                                            && t.HospitalId == hos.Id).Select(tt => tt.MedicalRecordNumber).FirstOrDefault()
                                        }).ToList();
            if (list != null && list.Count > 0)
            {
                foreach (var item in list)
                {
                    if (item.value == "0")
                    {
                        item.value = "";
                    }
                }

                retList = list;
            }

            return retList;
        }

        public phonesForDemogrEdit_n GetPhonInfoFromDB(int demographicId)
        {
            demographicId = context.Demographics.
                            Where(t => t.PatientRecordId == demographicId).Select(tt => tt.Id).FirstOrDefault();

            IEnumerable<DemographicsPhoneNumber> phonesSet = context.PhoneNumber;
            DemographicsPhoneNumber phon_R_ = phonesSet.Where(t => t.DemographicId == demographicId).
                Where(t => t.typeOfPhoneNumber == PhoneNumberType.R).OrderByDescending(tt => tt.Id).FirstOrDefault();
            DemographicsPhoneNumber phon_C_ = phonesSet.Where(t => t.DemographicId == demographicId).
                Where(t => t.typeOfPhoneNumber == PhoneNumberType.C).OrderByDescending(tt => tt.Id).FirstOrDefault();
            DemographicsPhoneNumber phon_W_ = phonesSet.Where(t => t.DemographicId == demographicId).
                Where(t => t.typeOfPhoneNumber == PhoneNumberType.W).OrderByDescending(tt => tt.Id).FirstOrDefault();

            phoneFromDB_n phon_R = new phoneFromDB_n();
            if (phon_R_ != null)
            {
                phon_R.phoneNumber = phon_R_.phoneNumber;
                phon_R.extention = phon_R_.extention;
                phon_R.faxNumber = phon_R_.faxNumber;
                phon_R.typeOfPhoneNumber = phon_R_.typeOfPhoneNumber;
            }
            phoneFromDB_n phon_C = new phoneFromDB_n();
            if (phon_C_ != null)
            {
                phon_C.phoneNumber = phon_C_.phoneNumber;
                phon_C.extention = phon_C_.extention;
                phon_C.faxNumber = phon_C_.faxNumber;
                phon_C.typeOfPhoneNumber = phon_C_.typeOfPhoneNumber;
            }

            phoneFromDB_n phon_W = new phoneFromDB_n();

            if (phon_W_ != null)
            {
                phon_W.phoneNumber = phon_W_.phoneNumber;
                phon_W.extention = phon_W_.extention;
                phon_W.faxNumber = phon_W_.faxNumber;
                phon_W.typeOfPhoneNumber = phon_W_.typeOfPhoneNumber;
            }


            phonesForDemogrEdit_n ph = new phonesForDemogrEdit_n();
            ph.phone = phon_R == null ? "" : phon_R.phoneNumber;
            ph.faxPharmacy = phon_R == null ? "" : phon_R.faxNumber;
            ph.extentionPhone = phon_R == null ? "" : phon_R.extention;
            ph.phoneContact = phon_W == null ? "" : phon_W.phoneNumber;
            ph.extentionContact = phon_W == null ? "" : phon_W.extention;
            ph.cell = phon_C == null ? "" : phon_C.phoneNumber;

            return ph;
        }

        public helthCardInfo_n GetHelthCardInfoFromDB(int patientRecordId)
        {
            DemographicsHealthCard helthCard_ = (from dmg in context.Demographics
                                                 join dhc in context.HealthCards on dmg.Id equals dhc.DemographicId
                                                 where dmg.PatientRecordId == patientRecordId
                                                 orderby dhc.Id descending
                                                 select dhc).FirstOrDefault();

            helthCardInfo_n helthCard = null;
            if (helthCard_ != null)
            {
                helthCard = new helthCardInfo_n();
                helthCard.number = helthCard_.number;
                helthCard.dateIssued = helthCard_.dateIssued;
                helthCard.lastValidated = helthCard_.lastValidated;
                helthCard.expirydate = helthCard_.expirydate;
                helthCard.provinceCode = helthCard_.provinceCode;
                helthCard.version = helthCard_.version;
            }

            return helthCard;
        }

        private List<name_value_id> GetReturnAddressList_n(List<DemographicsAddress> dbAddresses)
        {
            List<name_value_id> tempList = new List<name_value_id>();
            if (dbAddresses != null && dbAddresses.Count > 0)
            {
                var demoActiveAddresses = dbAddresses.Where(w => w.IsActive).ToList();

                foreach (var item in demoActiveAddresses)
                {
                    name_value_id addF = new name_value_id();
                    addF.id = item.Id;
                    addF.name = (item.addressLine1 ?? "") + " " + (item.addressLine2 ?? "");
                    addF.name1 = item.postalCode ?? "";
                    addF.name2 = GetProvinceNewString(item.province, item.country);
                    addF.name3 = item.country ?? "";
                    addF.name4 = item.city ?? "";
                    addF.value = EnumExtensions.GetDiscriptionName(item.addressType);
                    addF.IsActive = item.IsActive;
                    tempList.Add(addF);
                }
            }

            return tempList;
        }

        private string GetProvinceNewString(string province, string country)
        {
            string retVal = null;

            if (province != null)
            {
                province = province.Replace(" ", "").Replace("-", "");
                if (province.Length == 4)
                {
                    retVal = province.Substring(2);
                }
                else
                {
                    retVal = province;
                }
            }


            return retVal;
        }

        private Province GetProvinceNew(string province, string country)
        {
            if (province == "Select...")
            {
                province = "Select";
            }
            else
            {
                if (country == "Canada")
                {
                    province = "CA" + province;
                }
                else if (country == "USA")
                {
                    province = "US" + province;
                }
                else
                {
                    province = "CA" + province;
                }
            }

            Province retEnum = Province.CAON;
            try
            {
                if (province != null && province != "")
                {
                    province = province.Replace(" ", "");
                }
                retEnum = (Province)Enum.Parse(typeof(Province), province);
            }
            catch (Exception)
            {
            }

            return retEnum;
        }

        private Country GetCountry(string country)
        {
            Country retEnum = Country.Canada;
            try
            {
                retEnum = (Country)Enum.Parse(typeof(Country), country ?? "");
            }
            catch (Exception)
            {
            }

            return retEnum;
        }

        public NextOfKeen_n GetNXK(NextOfKeen_n demModel)
        {
            DemographicsNextOfKin testKeen = null;

            demModel.nxk_patientId = context.Demographics.
                    Where(t => t.PatientRecordId == demModel.nxk_patientId).Select(tt => tt.Id).FirstOrDefault();

            if (demModel.nxk_type == "load")
            {
                testKeen = context.DemographicsContacts.
                        Where(t => t.DemographicId == demModel.nxk_patientId && t.IsActive == true && t.IsRemoved == false).FirstOrDefault();

                if (testKeen == null)
                {
                    testKeen = context.DemographicsContacts.
                        Where(t => t.DemographicId == demModel.nxk_patientId && t.IsRemoved == false).OrderByDescending(tt => tt.Id).FirstOrDefault();
                }
            }
            else if (demModel.nxk_type == "loadById")
            {
                testKeen = context.DemographicsContacts.
                         Where(t => t.Id == demModel.nxk_id).FirstOrDefault();
            }



            if (testKeen != null)
            {
                demModel.nxk_id = testKeen.Id;
                demModel.nxk_contactPurpose = testKeen.contactPurpose ?? "";
                demModel.nxk_contactPurposeEnum = testKeen.ContactPurposeEnum;
                demModel.nxk_emailAddress = testKeen.emailAddress ?? "";
                demModel.nxk_firstName = testKeen.firstName ?? "";
                demModel.nxk_middleName = testKeen.middleName ?? "";
                demModel.nxk_lastName = testKeen.lastName ?? "";
                demModel.nxk_notes = testKeen.notes ?? "";
                demModel.IsActive = testKeen.IsActive;
                demModel.IsRemoved = testKeen.IsRemoved;

                DemographicsContactPhoneNumber phone1 = context.DemographicsContactPhoneNumbers.
                    Where(t => t.DemographicsContactId == testKeen.Id && t.IsActive == true && t.IsRemoved == false).FirstOrDefault();

                demModel.nxk_phone = "";
                demModel.nxk_ext = "";
                if (phone1 != null)
                {
                    demModel.nxk_phone = phone1.contactPhoneNumber == null ? "" : phone1.contactPhoneNumber;
                    demModel.nxk_ext = phone1.extention == null ? "" : phone1.extention;
                    demModel.nxk_phone = demModel.nxk_phone + (demModel.nxk_ext == "" ? "" : " ext " + demModel.nxk_ext);
                }

                if (demModel.nxk_phone == "")
                {
                    DemographicsContactPhoneNumber phone = context.DemographicsContactPhoneNumbers.Where(t => t.DemographicsContactId == testKeen.Id).
                             OrderByDescending(tt => tt.Id).FirstOrDefault();

                    demModel.nxk_phone = "";
                    demModel.nxk_ext = "";
                    if (phone != null)
                    {
                        demModel.nxk_phone = phone.contactPhoneNumber == null ? "" : phone.contactPhoneNumber;
                        demModel.nxk_ext = phone.extention == null ? "" : phone.extention;
                    }
                }

                #region Create List of
                Demographic dmmm = new Demographic();
                //demModel.nxksList = CreateListOfContacts(demModel.nxk_patientId);

                demModel.nxksList = (from demCont in context.DemographicsContacts
                                     where demCont.DemographicId == demModel.nxk_patientId
                                     select new name_id_nkx
                                     {
                                         id = demCont.Id,
                                         name = (demCont.lastName ?? "") + ", " + (demCont.firstName ?? ""),
                                         contactPurpose = demCont.contactPurpose ?? "",
                                         phoneNumbers = (context.DemographicsContactPhoneNumbers.
                                                         Where(t => t.DemographicsContactId == demCont.Id && t.IsRemoved == false).
                                                         OrderByDescending(tt => tt.Id).ToList()),
                                         phone = "",
                                         phoneType = "",
                                         phoneExt = "",
                                         purposeEnum = demCont.ContactPurposeEnum,
                                         purposeEnumId = (int)demCont.ContactPurposeEnum,
                                         IsActive = demCont.IsActive,
                                         isRemoved = demCont.IsRemoved,
                                         FName = (demCont.firstName ?? ""),
                                         LName = (demCont.lastName ?? ""),
                                         emailAddress = (demCont.emailAddress ?? ""),
                                         notes = (demCont.notes ?? "")
                                     }).ToList();

                if (demModel.nxksList != null && demModel.nxksList.Count > 0)
                {
                    List<name_id_nkx> removedList = new List<name_id_nkx>();

                    foreach (var item in demModel.nxksList)
                    {
                        item.stringOfPhNumbers = "";

                        //filter deleted
                        if (item.isRemoved == true)
                        {
                            removedList.Add(item);
                            continue;
                        }

                        if (item.purposeEnum == ContactPurpose.O)
                        {
                            item.purpose = (item.contactPurpose == null || item.contactPurpose == "") ? "Other" : item.contactPurpose;
                        }
                        else
                        {
                            item.purpose = EnumExtensions.GetDiscriptionName(item.purposeEnum);
                            item.purpose = item.purpose == "Select..." ? "" : item.purpose;
                        }


                        if (item.phoneNumbers != null && item.phoneNumbers.Count > 0)
                        {
                            DemographicsContactPhoneNumber ph_mun_prime = item.phoneNumbers.Where(t => t.IsActive == true && t.IsRemoved == false).FirstOrDefault();
                            if (ph_mun_prime != null)
                            {
                                item.phone = ph_mun_prime.contactPhoneNumber ?? "";
                                item.phoneExt = ph_mun_prime.extention ?? "";
                                item.phone = item.phone + (item.phoneExt == "" ? "" : " ext " + item.phoneExt);
                                item.phoneType = EnumExtensions.GetDiscriptionName(ph_mun_prime.typeOfPhoneNumber);
                                item.phoneId = ph_mun_prime.Id;
                                item.phoneTypeEnumId = (int)ph_mun_prime.typeOfPhoneNumber;
                            }
                            else
                            {
                                DemographicsContactPhoneNumber ph_mun = item.phoneNumbers.Where(t => t.IsRemoved == false).FirstOrDefault();
                                if (ph_mun != null)
                                {
                                    item.phone = ph_mun.contactPhoneNumber ?? "";
                                    item.phoneExt = ph_mun.extention ?? "";
                                    item.phone = item.phone + (item.phoneExt == "" ? "" : " ext " + item.phoneExt);
                                    item.phoneType = EnumExtensions.GetDiscriptionName(ph_mun.typeOfPhoneNumber);
                                    item.phoneId = ph_mun.Id;
                                    item.phoneTypeEnumId = (int)ph_mun.typeOfPhoneNumber;
                                }
                            }

                            item.stringOfPhNumbers = CreateListOfPhAsString(item.phoneNumbers, item.id, item.name);
                            item.phoneNumbers = null;
                        }
                    }

                    if (removedList.Count > 0)
                    {
                        List<int> Ids = removedList.Select(t => t.id).ToList();
                        demModel.nxksList.RemoveAll(t => Ids.Contains(t.id));
                    }


                    name_id_nkx itemActive = demModel.nxksList.Where(t => t.IsActive == true && t.isRemoved == false).FirstOrDefault();
                    if (itemActive != null)
                    {
                        demModel.nxksList.Remove(itemActive);
                        List<name_id_nkx> tempList = new List<name_id_nkx>() { itemActive };
                        tempList.AddRange(demModel.nxksList);
                        demModel.nxksList = tempList;
                    }
                }




                #endregion

                demModel.nxk_message = "";

                return demModel;
            }
            else
            {
                return null;
            }
        }

        private List<name_id_nkx> CreateListOfPatientContacts(List<DemographicsNextOfKin> list)
        {
            List<name_id_nkx> retList = null;
            List<name_id_nkx> nxksList = (from demCont in list
                                          select new name_id_nkx
                                          {
                                              id = demCont.Id,
                                              name = (demCont.lastName ?? "") + ", " + (demCont.firstName ?? ""),
                                              contactPurpose = demCont.contactPurpose ?? "",
                                              phoneNumbers = (demCont.contactPhoneNumbers.
                                                    Where(t => t.DemographicsContactId == demCont.Id && t.IsRemoved == false).
                                                    OrderByDescending(tt => tt.Id).ToList()),
                                              //phoneNumbers = (context.DemographicsContactPhoneNumbers.
                                              //      Where(t => t.DemographicsContactId == demCont.Id && t.IsRemoved == false).
                                              //      OrderByDescending(tt => tt.Id).ToList()),
                                              phone = "",
                                              phoneType = "",
                                              phoneExt = "",
                                              purposeEnum = demCont.ContactPurposeEnum,
                                              purposeEnumId = (int)demCont.ContactPurposeEnum,
                                              IsActive = demCont.IsActive,
                                              isRemoved = demCont.IsRemoved,
                                              FName = (demCont.firstName ?? ""),
                                              LName = (demCont.lastName ?? ""),
                                              emailAddress = (demCont.emailAddress ?? ""),
                                              notes = (demCont.notes ?? "")
                                          }).ToList();


            retList = FormatListOfPatientContacts(nxksList);



            return retList;
        }

        //method should be used for all contact table format (ajax edit, insert ...)
        private List<name_id_nkx> FormatListOfPatientContacts(List<name_id_nkx> nxksList)
        {
            if (nxksList != null && nxksList.Count > 0)
            {
                List<name_id_nkx> removedList = new List<name_id_nkx>();

                foreach (var item in nxksList)
                {
                    item.stringOfPhNumbers = "";

                    //filter deleted
                    if (item.isRemoved == true)
                    {
                        removedList.Add(item);
                        continue;
                    }

                    if (item.purposeEnum == ContactPurpose.O)
                    {
                        item.purpose = (item.contactPurpose == null || item.contactPurpose == "") ? "Other" : item.contactPurpose;
                    }
                    else
                    {
                        item.purpose = EnumExtensions.GetDiscriptionName(item.purposeEnum);
                        item.purpose = item.purpose == "Select..." ? "" : item.purpose;
                    }


                    if (item.phoneNumbers != null && item.phoneNumbers.Count > 0)
                    {
                        DemographicsContactPhoneNumber ph_mun_prime = item.phoneNumbers.Where(t => t.IsActive == true && t.IsRemoved == false).FirstOrDefault();
                        if (ph_mun_prime != null)
                        {
                            item.phone = ph_mun_prime.contactPhoneNumber ?? "";
                            item.phoneExt = ph_mun_prime.extention ?? "";
                            item.phone = item.phone + (item.phoneExt == "" ? "" : " ext " + item.phoneExt);
                            item.phoneType = EnumExtensions.GetDiscriptionName(ph_mun_prime.typeOfPhoneNumber);
                            item.phoneId = ph_mun_prime.Id;
                            item.phoneTypeEnumId = (int)ph_mun_prime.typeOfPhoneNumber;
                        }
                        else
                        {
                            DemographicsContactPhoneNumber ph_mun = item.phoneNumbers.Where(t => t.IsRemoved == false).FirstOrDefault();
                            if (ph_mun != null)
                            {
                                item.phone = ph_mun.contactPhoneNumber ?? "";
                                item.phoneExt = ph_mun.extention ?? "";
                                item.phone = item.phone + (item.phoneExt == "" ? "" : " ext " + item.phoneExt);
                                item.phoneType = EnumExtensions.GetDiscriptionName(ph_mun.typeOfPhoneNumber);
                                item.phoneId = ph_mun.Id;
                                item.phoneTypeEnumId = (int)ph_mun.typeOfPhoneNumber;
                            }
                        }

                        item.stringOfPhNumbers = CreateListOfPhAsString(item.phoneNumbers, item.id, item.name);
                        item.phoneNumbers = null;
                    }
                }

                if (removedList.Count > 0)
                {
                    List<int> Ids = removedList.Select(t => t.id).ToList();
                    nxksList.RemoveAll(t => Ids.Contains(t.id));
                }


                name_id_nkx itemActive = nxksList.Where(t => t.IsActive == true && t.isRemoved == false).FirstOrDefault();
                if (itemActive != null)
                {
                    nxksList.Remove(itemActive);
                    List<name_id_nkx> tempList = new List<name_id_nkx>() { itemActive };
                    tempList.AddRange(nxksList);
                    nxksList = tempList;
                }
            }

            return nxksList;
        }

        public NextOfKeen_n GetNXK_Copy(NextOfKeen_n demModel)
        {
            DemographicsNextOfKin testKeen = null;

            demModel.nxk_patientId = context.Demographics.
                    Where(t => t.PatientRecordId == demModel.nxk_patientId).Select(tt => tt.Id).FirstOrDefault();

            if (demModel.nxk_type == "load")
            {
                testKeen = context.DemographicsContacts.
                        Where(t => t.DemographicId == demModel.nxk_patientId && t.IsActive == true && t.IsRemoved == false).FirstOrDefault();

                if (testKeen == null)
                {
                    testKeen = context.DemographicsContacts.
                        Where(t => t.DemographicId == demModel.nxk_patientId && t.IsRemoved == false).OrderByDescending(tt => tt.Id).FirstOrDefault();
                }
            }
            else if (demModel.nxk_type == "loadById")
            {
                testKeen = context.DemographicsContacts.
                         Where(t => t.Id == demModel.nxk_id).FirstOrDefault();
            }



            if (testKeen != null)
            {
                demModel.nxk_id = testKeen.Id;
                demModel.nxk_contactPurpose = testKeen.contactPurpose ?? "";
                demModel.nxk_contactPurposeEnum = testKeen.ContactPurposeEnum;
                demModel.nxk_emailAddress = testKeen.emailAddress ?? "";
                demModel.nxk_firstName = testKeen.firstName ?? "";
                demModel.nxk_middleName = testKeen.middleName ?? "";
                demModel.nxk_lastName = testKeen.lastName ?? "";
                demModel.nxk_notes = testKeen.notes ?? "";
                demModel.IsActive = testKeen.IsActive;
                demModel.IsRemoved = testKeen.IsRemoved;

                DemographicsContactPhoneNumber phone1 = context.DemographicsContactPhoneNumbers.
                    Where(t => t.DemographicsContactId == testKeen.Id && t.IsActive == true && t.IsRemoved == false).FirstOrDefault();

                demModel.nxk_phone = "";
                demModel.nxk_ext = "";
                if (phone1 != null)
                {
                    demModel.nxk_phone = phone1.contactPhoneNumber == null ? "" : phone1.contactPhoneNumber;
                    demModel.nxk_ext = phone1.extention == null ? "" : phone1.extention;
                    demModel.nxk_phone = demModel.nxk_phone + (demModel.nxk_ext == "" ? "" : " ext " + demModel.nxk_ext);
                }

                if (demModel.nxk_phone == "")
                {
                    DemographicsContactPhoneNumber phone = context.DemographicsContactPhoneNumbers.Where(t => t.DemographicsContactId == testKeen.Id).
                             OrderByDescending(tt => tt.Id).FirstOrDefault();

                    demModel.nxk_phone = "";
                    demModel.nxk_ext = "";
                    if (phone != null)
                    {
                        demModel.nxk_phone = phone.contactPhoneNumber == null ? "" : phone.contactPhoneNumber;
                        demModel.nxk_ext = phone.extention == null ? "" : phone.extention;
                    }
                }

                #region Create List of
                demModel.nxksList = (from demCont in context.DemographicsContacts
                                     where demCont.DemographicId == demModel.nxk_patientId
                                     select new name_id_nkx
                                     {
                                         id = demCont.Id,
                                         name = (demCont.lastName ?? "") + ", " + (demCont.firstName ?? ""),
                                         contactPurpose = demCont.contactPurpose ?? "",
                                         phoneNumbers = (context.DemographicsContactPhoneNumbers.
                                                         Where(t => t.DemographicsContactId == demCont.Id && t.IsRemoved == false).
                                                         OrderByDescending(tt => tt.Id).ToList()),
                                         phone = "",
                                         phoneType = "",
                                         phoneExt = "",
                                         purposeEnum = demCont.ContactPurposeEnum,
                                         purposeEnumId = (int)demCont.ContactPurposeEnum,
                                         IsActive = demCont.IsActive,
                                         isRemoved = demCont.IsRemoved,
                                         FName = (demCont.firstName ?? ""),
                                         LName = (demCont.lastName ?? ""),
                                         emailAddress = (demCont.emailAddress ?? ""),
                                         notes = (demCont.notes ?? "")
                                     }).ToList();

                if (demModel.nxksList != null && demModel.nxksList.Count > 0)
                {
                    List<name_id_nkx> removedList = new List<name_id_nkx>();

                    foreach (var item in demModel.nxksList)
                    {
                        item.stringOfPhNumbers = "";

                        //filter deleted
                        if (item.isRemoved == true)
                        {
                            removedList.Add(item);
                            continue;
                        }

                        if (item.purposeEnum == ContactPurpose.O)
                        {
                            item.purpose = (item.contactPurpose == null || item.contactPurpose == "") ? "Other" : item.contactPurpose;
                        }
                        else
                        {
                            item.purpose = EnumExtensions.GetDiscriptionName(item.purposeEnum);
                            item.purpose = item.purpose == "Select..." ? "" : item.purpose;
                        }


                        if (item.phoneNumbers != null && item.phoneNumbers.Count > 0)
                        {
                            DemographicsContactPhoneNumber ph_mun_prime = item.phoneNumbers.Where(t => t.IsActive == true && t.IsRemoved == false).FirstOrDefault();
                            if (ph_mun_prime != null)
                            {
                                item.phone = ph_mun_prime.contactPhoneNumber ?? "";
                                item.phoneExt = ph_mun_prime.extention ?? "";
                                item.phone = item.phone + (item.phoneExt == "" ? "" : " ext " + item.phoneExt);
                                item.phoneType = EnumExtensions.GetDiscriptionName(ph_mun_prime.typeOfPhoneNumber);
                                item.phoneId = ph_mun_prime.Id;
                                item.phoneTypeEnumId = (int)ph_mun_prime.typeOfPhoneNumber;
                            }
                            else
                            {
                                DemographicsContactPhoneNumber ph_mun = item.phoneNumbers.Where(t => t.IsRemoved == false).FirstOrDefault();
                                if (ph_mun != null)
                                {
                                    item.phone = ph_mun.contactPhoneNumber ?? "";
                                    item.phoneExt = ph_mun.extention ?? "";
                                    item.phone = item.phone + (item.phoneExt == "" ? "" : " ext " + item.phoneExt);
                                    item.phoneType = EnumExtensions.GetDiscriptionName(ph_mun.typeOfPhoneNumber);
                                    item.phoneId = ph_mun.Id;
                                    item.phoneTypeEnumId = (int)ph_mun.typeOfPhoneNumber;
                                }
                            }

                            item.stringOfPhNumbers = CreateListOfPhAsString(item.phoneNumbers, item.id, item.name);
                            item.phoneNumbers = null;
                        }
                    }

                    if (removedList.Count > 0)
                    {
                        List<int> Ids = removedList.Select(t => t.id).ToList();
                        demModel.nxksList.RemoveAll(t => Ids.Contains(t.id));
                    }


                    name_id_nkx itemActive = demModel.nxksList.Where(t => t.IsActive == true && t.isRemoved == false).FirstOrDefault();
                    if (itemActive != null)
                    {
                        demModel.nxksList.Remove(itemActive);
                        List<name_id_nkx> tempList = new List<name_id_nkx>() { itemActive };
                        tempList.AddRange(demModel.nxksList);
                        demModel.nxksList = tempList;
                    }
                }
                #endregion

                demModel.nxk_message = "";

                return demModel;
            }
            else
            {
                return null;
            }
        }

        private string CreateListOfPhAsString(List<DemographicsContactPhoneNumber> phoneNumbers, int id, string name)
        {
            DemographicsContactPhoneNumber itemActive = phoneNumbers.Where(t => t.IsActive == true && t.IsRemoved == false).FirstOrDefault();
            if (itemActive != null)
            {
                phoneNumbers.Remove(itemActive);
                List<DemographicsContactPhoneNumber> tempList = new List<DemographicsContactPhoneNumber>() { itemActive };
                tempList.AddRange(phoneNumbers);
                phoneNumbers = tempList;
            }


            if (name == null || name == "")
            {
                name = "_, _";
            }

            string retVal = "<div><table border='1' style='width: 100 % '>";
            foreach (var item in phoneNumbers)
            {
                retVal += "<tr><td class='click_me ph_num showHand' width='50%' title='Edith Phone'>"
                    + "<input class='ph_num_phid' type='hidden' value='" + item.Id + "' />"
                    + "<input class='ph_num_conid' type='hidden' value='" + item.DemographicsContactId + "' />"
                    + "<input class='ph_num_ph_typeid' type='hidden' value='" + (int)item.typeOfPhoneNumber + "' />"
                    + "<input class='ph_num_con_name' type='hidden' value='" + name + "' />"
                    + "<input class='ph_num_name' type='hidden' value='" + item.contactPhoneNumber + ((item.extention == null || item.extention == "") ? "" : " ext " + item.extention) + "' />"
                    + item.contactPhoneNumber + ((item.extention == null || item.extention == "") ? "" : " ext " + item.extention)
                    + "</td>" +
                    "<td width='50%'>" + EnumExtensions.GetDiscriptionName(item.typeOfPhoneNumber) + "</td></tr>";
            }

            return retVal + "</table></div>";
        }

        public demographicsDoctorData_n GetMainDocFromDB_(int patientRecordId)
        {
            demographicsDoctorData_n mainDocActive = (from pt in context.Demographics
                                                      join pr in context.PatientRecords on pt.PatientRecordId equals pr.Id
                                                      join fdoc in context.DemographicsMainResponsiblePhysicians on pt.Id equals fdoc.DemographicId
                                                      join ex in context.ExternalDoctors on fdoc.ExternalDoctorId equals ex.Id
                                                      where pr.Id == patientRecordId && fdoc.IsActive == true
                                                      orderby fdoc.Id descending
                                                      select new demographicsDoctorData_n
                                                      {
                                                          mainResponsibleDocId = fdoc.PracticeDoctorId,
                                                          docFullName = (ex.lastName ?? "") + ", " + (ex.firstName ?? ""),
                                                          mainDoctorHid = fdoc.PracticeDoctorId.ToString()
                                                      }).FirstOrDefault();

            demographicsDoctorData_n mainDoc = new demographicsDoctorData_n();

            if (mainDocActive != null)
            {
                mainDoc = mainDocActive;
            }

            DemographicsEnrollment enrl = (from en in context.DemographicsEnrollments
                                           join md in context.DemographicsMainResponsiblePhysicians on en.DemographicsMRPId equals md.Id
                                           join dem in context.Demographics on md.DemographicId equals dem.Id
                                           where dem.PatientRecordId == patientRecordId
                                           && (en.enrollmentTerminationDate == null
                                           || en.enrollmentTerminationDate == DateTime.MaxValue
                                           || en.enrollmentTerminationDate == DateTime.MinValue)
                                           orderby en.Id descending
                                           select en).FirstOrDefault();
            if (enrl != null)
            {
                string name = "";
                //if (enrl.enrollmentTerminationDate == DateTime.MaxValue || enrl.enrollmentTerminationDate == DateTime.MinValue)
                //{
                name = (from MRP in context.DemographicsMainResponsiblePhysicians
                        join ex in context.ExternalDoctors on MRP.ExternalDoctorId equals ex.Id
                        where MRP.Id == enrl.DemographicsMRPId
                        select (ex.lastName ?? "") + ", " + (ex.firstName ?? "")).FirstOrDefault();
                //}

                mainDoc.EnrolledDocName = name ?? "";

                mainDoc.IsPatientHasEnroll = true;
            }

            return mainDoc;
        }

        private DoctorsData GetRefferalDocListFromDB(int patientRecordId)
        {
            DoctorsData retValue = new DoctorsData();

            List<DoctorsData> docList = (from pt in context.Demographics
                                         join fdoc in context.DemographicsDefaultReferralDoctors on pt.Id equals fdoc.DemographicId
                                         join ex in context.ExternalDoctors on fdoc.ExternalDoctorId equals ex.Id
                                         where pt.PatientRecordId == patientRecordId && fdoc.IsRemoved == false
                                         orderby fdoc.Id descending
                                         select new DoctorsData
                                         {
                                             Id = ex.Id,
                                             text = (ex.lastName ?? "") + ", " + (ex.firstName ?? ""),
                                             isActive = fdoc.IsActive,
                                             isRemoved = fdoc.IsRemoved
                                         }).GroupBy(g => g.Id).Select(s => new DoctorsData { Id = s.Key, text = s.FirstOrDefault().text, isActive = s.FirstOrDefault().isActive, isRemoved = s.FirstOrDefault().isRemoved }).ToList();


            if (docList != null && docList.Count > 0)
            {
                var activeDoctors = docList.Where(w => w.isActive).ToList();
                var inactiveDoctors = docList.Where(w => !w.isActive && activeDoctors.TrueForAll(a => a.Id != w.Id)).Distinct().ToList();
                docList = activeDoctors.Union(inactiveDoctors).ToList();
                retValue.list = docList.Select(t => new ValueTextBool()
                { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();
            }

            return retValue;
        }

        private DoctorsData GetFamilyDocListFromDB(int patientRecordId)
        {
            DoctorsData retValue = null;

            List<DoctorsData> docList = (from pt in context.Demographics
                                         join fdoc in context.DemographicsFamilyDoctors on pt.Id equals fdoc.DemographicId
                                         join ex in context.ExternalDoctors on fdoc.ExternalDoctorId equals ex.Id
                                         where pt.PatientRecordId == patientRecordId && fdoc.IsRemoved == false
                                         orderby fdoc.Id descending
                                         select new DoctorsData
                                         {
                                             text = (ex.lastName ?? "") + ", " + (ex.firstName ?? ""),
                                             Id = ex.Id,
                                             isActive = fdoc.IsActive,
                                             isRemoved = fdoc.IsRemoved
                                         }).ToList();

            if (docList != null && docList.Count > 0)
            {
                if (docList.Count == 1)
                {
                    retValue = docList[0];

                    retValue.list = docList.Select(t => new ValueTextBool()
                    { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();
                }
                else
                {
                    DoctorsData primeDoc = docList.Where(t => t.isActive == true).FirstOrDefault();
                    if (primeDoc != null)
                    {
                        docList.Remove(primeDoc);
                        List<DoctorsData> tempList = new List<DoctorsData>() { primeDoc };
                        tempList.AddRange(docList);
                        retValue = tempList[0];
                        retValue.list = tempList.Select(t => new ValueTextBool()
                        { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();

                    }
                    else
                    {
                        retValue = docList[0];
                        retValue.list = docList.Select(t => new ValueTextBool()
                        { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();
                    }
                }
            }

            return retValue;
        }

        private DoctorsData GetAssocDocListFromDB(int patientRecordId)
        {
            DoctorsData retValue = null;

            List<DoctorsData> docList = (from pt in context.Demographics
                                         join fdoc in context.DemographicsAssociatedDoctors on pt.Id equals fdoc.DemographicId
                                         join ex in context.ExternalDoctors on fdoc.ExternalDoctorId equals ex.Id
                                         where pt.PatientRecordId == patientRecordId && fdoc.IsRemoved == false
                                         orderby fdoc.Id descending
                                         select new DoctorsData
                                         {
                                             text = (ex.lastName ?? "") + ", " + (ex.firstName ?? ""),
                                             Id = ex.Id,
                                             isActive = fdoc.IsActive,
                                             isRemoved = fdoc.IsRemoved
                                         }).ToList();

            if (docList != null && docList.Count > 0)
            {
                if (docList.Count == 1)
                {
                    retValue = docList[0];

                    retValue.list = docList.Select(t => new ValueTextBool()
                    { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();
                }
                else
                {
                    DoctorsData primeDoc = docList.Where(t => t.isActive == true).FirstOrDefault();
                    if (primeDoc != null)
                    {
                        docList.Remove(primeDoc);
                        List<DoctorsData> tempList = new List<DoctorsData>() { primeDoc };
                        tempList.AddRange(docList);
                        retValue = tempList[0];
                        retValue.list = tempList.Select(t => new ValueTextBool()
                        { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();

                    }
                    else
                    {
                        retValue = docList[0];
                        retValue.list = docList.Select(t => new ValueTextBool()
                        { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();
                    }
                }
            }

            return retValue;
        }

        //CheckNamesDOBCollusion
        public bool CheckNamesDOBCollusion(CheckDate data)
        {
            bool retVAl = true;

            DateTime dob = GetDOBAsDate(data.DateOfBirth);
            Gender gender = GetGender(data.GenderId);

            try
            {

                Demographic dem = (from dm in context.Demographics
                                   where (dm.firstName == data.FirstName &&
                                   dm.lastName == data.LastName
                                   //&&
                                   //dm.dateOfBirth == dob &&
                                   //dm.gender == gender
                                   )
                                   select dm).FirstOrDefault();

                if (dem == null)
                {
                    retVAl = false;
                }
            }
            catch (Exception ex)
            {
                string Msg = $" ### CheckNamesDOBCollusion {ex.ExceptionDetails()}";
                _log.Error(Msg);
            }

            return retVAl;
        }

        public string ChangeStatus(int activeId, int patientRecordId, int userid, string ipaddress)
        {
            string retVal = "";
            Demographic dem = context.Demographics.Where(t => t.PatientRecordId == patientRecordId).FirstOrDefault();
            if (dem != null)
            {
                try
                {
                    dem.StatusDate = DateTime.Now.Date;

                    context.Entry(dem).State = EntityState.Modified;
                    context.SaveChanges(userid, ipaddress);

                    retVal = DateTime.Now.Date.ToString("MMM dd, yyyy");
                }
                catch (Exception ex)
                {
                    string Msg = $" ### ChangeStatus {ex.ExceptionDetails()}";
                    _log.Error(Msg);
                }
            }


            return retVal;
        }

        private Gender GetGender(int genderId)
        {
            Gender retVal = Gender.O;
            try
            {
                retVal = (Gender)genderId;
                return retVal;
            }
            catch (Exception)
            {
                return retVal;
            }
        }

        private DateTime GetDOBAsDate(string dateOfBirth)
        {
            DateTime retVal = DateTime.MaxValue;
            try
            {
                retVal = Convert.ToDateTime(dateOfBirth);
                return retVal;
            }
            catch (Exception)
            {
                return retVal;
            }
        }
        /// <summary>
        /// we must move this method to IPracticeDoctorBLL
        /// </summary>
        /// <param name="term_"></param>
        /// <param name="practiceId"></param>
        /// <returns></returns>
        public List<VMLookupItem> SearchMainDocs(string term_, int practiceId)
        {
            List<ExternalDoctor> dbDoctors = new List<ExternalDoctor>();
            List<VMLookupItem> doctors = new List<VMLookupItem>();
            int n;
            bool isNumeric = int.TryParse(term_, out n);
            if (isNumeric)
            {
                doctors = (from ed in context.ExternalDoctors
                           join pd in context.PracticeDoctors on ed.Id equals pd.ExternalDoctorId
                           where (ed.OHIPPhysicianId.StartsWith(term_.Trim().ToLower()) && ed.active == true && pd.PracticeId == practiceId)
                           select new VMLookupItem
                           {
                               Text = (ed.lastName == null ? "" : ed.lastName.ToLower()) + ", " + (ed.firstName == null ? "" : ed.firstName.ToLower()),
                               Value = pd.Id.ToString()
                           }).ToList();
            }
            else if (term_.Contains(","))
            {
                string[] names = term_.Split(',');
                string lastname = names[0].Trim();
                string firstname = names[1].Trim();
                if ((!string.IsNullOrWhiteSpace(lastname)) && (string.IsNullOrWhiteSpace(firstname)))
                {
                    doctors = (from ed in context.ExternalDoctors
                               join pd in context.PracticeDoctors on ed.Id equals pd.ExternalDoctorId
                               where (ed.lastName.StartsWith(lastname) && ed.active == true && pd.PracticeId == practiceId)
                               select new VMLookupItem
                               {
                                   Text = (ed.lastName == null ? "" : ed.lastName.ToLower()) + ", " + (ed.firstName == null ? "" : ed.firstName.ToLower()),
                                   Value = pd.Id.ToString()
                               }).ToList();
                }
                else if ((string.IsNullOrWhiteSpace(lastname)) && (!string.IsNullOrWhiteSpace(firstname)))
                {
                    doctors = (from ed in context.ExternalDoctors
                               join pd in context.PracticeDoctors on ed.Id equals pd.ExternalDoctorId
                               where (ed.firstName.StartsWith(firstname) && ed.active == true && pd.PracticeId == practiceId)
                               select new VMLookupItem
                               {
                                   Text = (ed.lastName == null ? "" : ed.lastName.ToLower()) + ", " + (ed.firstName == null ? "" : ed.firstName.ToLower()),
                                   Value = pd.Id.ToString()
                               }).ToList();
                }
                else
                {
                    doctors = (from ed in context.ExternalDoctors
                               join pd in context.PracticeDoctors on ed.Id equals pd.ExternalDoctorId
                               where (ed.firstName.StartsWith(firstname) && ed.lastName.StartsWith(lastname)
                               && ed.active == true && pd.PracticeId == practiceId)
                               select new VMLookupItem
                               {
                                   Text = (ed.lastName == null ? "" : ed.lastName.ToLower()) + ", " + (ed.firstName == null ? "" : ed.firstName.ToLower()),
                                   Value = pd.Id.ToString()
                               }).ToList();
                }
            }
            else
            {
                doctors = (from ed in context.ExternalDoctors
                           join pd in context.PracticeDoctors on ed.Id equals pd.ExternalDoctorId
                           where (ed.lastName.StartsWith(term_.Trim().ToLower()) && ed.active == true && pd.PracticeId == practiceId)
                           select new VMLookupItem
                           {
                               Text = (ed.lastName == null ? "" : ed.lastName.ToLower()) + ", " + (ed.firstName == null ? "" : ed.firstName.ToLower()),
                               Value = pd.Id.ToString()
                           }).ToList();
            }

            return doctors;
        }
        /// <summary>
        /// We must move this method to IPracticeDoctorBLL
        /// </summary>
        /// <param name="term_"></param>
        /// <returns></returns>
        public List<VMLookupItem> SearchDemographicsDocs(string term_)
        {
            int topRecords = 15;
            List<ExternalDoctor> dbDoctors = new List<ExternalDoctor>();
            List<VMLookupItem> doctors = new List<VMLookupItem>();
            int n;
            bool isNumeric = int.TryParse(term_, out n);
            if (isNumeric)
            {
                dbDoctors = context.ExternalDoctors.
                Where(d => d.OHIPPhysicianId.StartsWith(term_) && d.active).Take(topRecords).ToList();

            }
            else if (term_.Contains(","))
            {
                string[] names = term_.Split(',');
                string lastname = names[0].Trim();
                string firstname = names[1].Trim();
                if ((!string.IsNullOrWhiteSpace(lastname)) && (string.IsNullOrWhiteSpace(firstname)))
                {
                    dbDoctors = context.ExternalDoctors.Where(d => d.lastName.StartsWith(lastname) && d.active).Take(topRecords).ToList();
                }
                else if ((string.IsNullOrWhiteSpace(lastname)) && (!string.IsNullOrWhiteSpace(firstname)))
                {
                    dbDoctors = context.ExternalDoctors.Where(d => d.firstName.StartsWith(firstname) && d.active).Take(topRecords).ToList();
                }
                else
                {
                    dbDoctors = context.ExternalDoctors.Where(d =>
                    d.lastName.StartsWith(lastname) &&
                    d.firstName.StartsWith(firstname)
                    && d.active
                    ).Take(topRecords).ToList();
                }
            }
            else
            {
                dbDoctors = context.ExternalDoctors.Where(d => d.lastName.StartsWith(term_.Trim()) && d.active).Take(topRecords).ToList();
            }


            foreach (var dbRefDoctor in dbDoctors)
            {
                var doctor = new VMLookupItem();
                doctor.Value = dbRefDoctor.Id.ToString();
                doctor.Text = (dbRefDoctor.lastName == null ? "" : dbRefDoctor.lastName) + ", " +
                    (dbRefDoctor.firstName == null ? "" : dbRefDoctor.firstName) + " - " + (dbRefDoctor.OHIPPhysicianId == null ? "" : dbRefDoctor.OHIPPhysicianId);
                doctors.Add(doctor);
            }

            return doctors;
        }

        public List<ValueTextBool> GetFamilyDocListAfterActiveation(PtnDocIds ptnDocIds)
        {

            List<ValueTextBool> retValue = new List<ValueTextBool>();

            DemographicsFamilyDoctor currDoctor = (from pt in context.Demographics
                                                   join fdoc in context.DemographicsFamilyDoctors on pt.Id equals fdoc.DemographicId
                                                   join ex in context.ExternalDoctors on fdoc.ExternalDoctorId equals ex.Id
                                                   where pt.PatientRecordId == ptnDocIds.PatientRecordId &&
                                                   fdoc.ExternalDoctorId == ptnDocIds.ExternalDoctorId &&
                                                   fdoc.IsRemoved == false
                                                   select fdoc).FirstOrDefault();

            if (currDoctor != null)
            {
                bool boolOfCurrent = currDoctor.IsActive;

                List<DemographicsFamilyDoctor> fDocList = (from pt in context.Demographics
                                                           join fdoc in context.DemographicsFamilyDoctors on pt.Id equals fdoc.DemographicId
                                                           join ex in context.ExternalDoctors on fdoc.ExternalDoctorId equals ex.Id
                                                           where pt.PatientRecordId == ptnDocIds.PatientRecordId &&
                                                           fdoc.IsRemoved == false &&
                                                           fdoc.IsActive == true
                                                           select fdoc).ToList();
                if (fDocList != null && fDocList.Count > 0)
                {
                    foreach (var item in fDocList)
                    {
                        item.IsActive = false;
                        context.Entry(item).State = EntityState.Modified;
                    }
                }

                currDoctor.IsActive = !boolOfCurrent;
                context.Entry(currDoctor).State = EntityState.Modified;

                context.SaveChanges();
            }



            List<DoctorsData> docList = (from pt in context.Demographics
                                         join fdoc in context.DemographicsFamilyDoctors on pt.Id equals fdoc.DemographicId
                                         join ex in context.ExternalDoctors on fdoc.ExternalDoctorId equals ex.Id
                                         where pt.PatientRecordId == ptnDocIds.PatientRecordId &&
                                         fdoc.IsRemoved == false
                                         orderby fdoc.Id descending
                                         select new DoctorsData
                                         {
                                             text = (ex.lastName ?? "") + ", " + (ex.firstName ?? ""),
                                             Id = ex.Id,
                                             isActive = fdoc.IsActive,
                                             isRemoved = fdoc.IsRemoved
                                         }).ToList();

            if (docList != null && docList.Count > 0)
            {
                if (docList.Count == 1)
                {
                    retValue = docList.Select(t => new ValueTextBool()
                    { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();
                }
                else
                {
                    DoctorsData primeDoc = docList.Where(t => t.isActive == true).FirstOrDefault();
                    if (primeDoc != null)
                    {
                        docList.Remove(primeDoc);
                        List<DoctorsData> tempList = new List<DoctorsData>() { primeDoc };
                        tempList.AddRange(docList);
                        retValue = tempList.Select(t => new ValueTextBool()
                        { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();

                    }
                    else
                    {
                        retValue = docList.Select(t => new ValueTextBool()
                        { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();
                    }
                }
            }

            return retValue;
        }
        public List<ValueTextBool> GetFamilyDocListAfterRemoval(PtnDocIds ptnDocIds)
        {

            List<ValueTextBool> retValue = new List<ValueTextBool>();

            DemographicsFamilyDoctor currDoctor = (from pt in context.Demographics
                                                   join fdoc in context.DemographicsFamilyDoctors on pt.Id equals fdoc.DemographicId
                                                   join ex in context.ExternalDoctors on fdoc.ExternalDoctorId equals ex.Id
                                                   where pt.PatientRecordId == ptnDocIds.PatientRecordId &&
                                                   fdoc.ExternalDoctorId == ptnDocIds.ExternalDoctorId &&
                                                   fdoc.IsRemoved == false
                                                   select fdoc).FirstOrDefault();

            if (currDoctor != null)
            {
                currDoctor.IsRemoved = !currDoctor.IsRemoved;
                context.Entry(currDoctor).State = EntityState.Modified;

                context.SaveChanges();
            }



            List<DoctorsData> docList = (from pt in context.Demographics
                                         join fdoc in context.DemographicsFamilyDoctors on pt.Id equals fdoc.DemographicId
                                         join ex in context.ExternalDoctors on fdoc.ExternalDoctorId equals ex.Id
                                         where pt.PatientRecordId == ptnDocIds.PatientRecordId &&
                                         fdoc.IsRemoved == false
                                         orderby fdoc.Id descending
                                         select new DoctorsData
                                         {
                                             text = (ex.lastName ?? "") + ", " + (ex.firstName ?? ""),
                                             Id = ex.Id,
                                             isActive = fdoc.IsActive,
                                             isRemoved = fdoc.IsRemoved
                                         }).ToList();

            if (docList != null && docList.Count > 0)
            {
                if (docList.Count == 1)
                {
                    retValue = docList.Select(t => new ValueTextBool()
                    { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();
                }
                else
                {
                    DoctorsData primeDoc = docList.Where(t => t.isActive == true).FirstOrDefault();
                    if (primeDoc != null)
                    {
                        docList.Remove(primeDoc);
                        List<DoctorsData> tempList = new List<DoctorsData>() { primeDoc };
                        tempList.AddRange(docList);
                        retValue = tempList.Select(t => new ValueTextBool()
                        { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();

                    }
                    else
                    {
                        retValue = docList.Select(t => new ValueTextBool()
                        { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();
                    }
                }
            }

            return retValue;
        }

        public DoctorsData GetDemographicsReferralDoctor(ActivateDemographicsReferralDoctorData request, int modifyingUserId, string ipAddress)
        {
            var referralDoctor = (from a in context.Demographics
                                  join b in context.DemographicsDefaultReferralDoctors on a.Id equals b.DemographicId
                                  where a.PatientRecordId == request.patientRecordId && b.ExternalDoctorId == request.externalDoctorId
                                  select b).FirstOrDefault();
            if (referralDoctor != null)
            {
                referralDoctor.IsActive = request.active;
                context.SaveChanges(modifyingUserId, ipAddress);
            }

            DoctorsData retVal = GetRefferalDocListFromDB(request.patientRecordId);
            return retVal;
        }

        public List<ValueTextBool> GetAssocDocListAfterActiveation(PtnDocIds ptnDocIds)
        {

            List<ValueTextBool> retValue = new List<ValueTextBool>();

            DemographicsAssociatedDoctor currDoctor = (from pt in context.Demographics
                                                       join fdoc in context.DemographicsAssociatedDoctors on pt.Id equals fdoc.DemographicId
                                                       join ex in context.ExternalDoctors on fdoc.ExternalDoctorId equals ex.Id
                                                       where pt.PatientRecordId == ptnDocIds.PatientRecordId &&
                                                       fdoc.ExternalDoctorId == ptnDocIds.ExternalDoctorId &&
                                                       fdoc.IsRemoved == false
                                                       select fdoc).FirstOrDefault();

            if (currDoctor != null)
            {
                bool boolOfCurrent = currDoctor.IsActive;

                currDoctor.IsActive = !boolOfCurrent;
                context.Entry(currDoctor).State = EntityState.Modified;

                context.SaveChanges();
            }



            List<DoctorsData> docList = (from pt in context.Demographics
                                         join fdoc in context.DemographicsAssociatedDoctors on pt.Id equals fdoc.DemographicId
                                         join ex in context.ExternalDoctors on fdoc.ExternalDoctorId equals ex.Id
                                         where pt.PatientRecordId == ptnDocIds.PatientRecordId &&
                                         fdoc.IsRemoved == false
                                         orderby fdoc.Id descending
                                         select new DoctorsData
                                         {
                                             text = (ex.lastName ?? "") + ", " + (ex.firstName ?? ""),
                                             Id = ex.Id,
                                             isActive = fdoc.IsActive,
                                             isRemoved = fdoc.IsRemoved
                                         }).ToList();

            if (docList != null && docList.Count > 0)
            {
                if (docList.Count == 1)
                {
                    retValue = docList.Select(t => new ValueTextBool()
                    { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();
                }
                else
                {
                    DoctorsData primeDoc = docList.Where(t => t.isActive == true).FirstOrDefault();
                    if (primeDoc != null)
                    {
                        docList.Remove(primeDoc);
                        List<DoctorsData> tempList = new List<DoctorsData>() { primeDoc };
                        tempList.AddRange(docList);
                        retValue = tempList.Select(t => new ValueTextBool()
                        { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();

                    }
                    else
                    {
                        retValue = docList.Select(t => new ValueTextBool()
                        { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();
                    }
                }
            }

            return retValue;
        }
        public List<ValueTextBool> GetAssocDocListAfterRemoval(PtnDocIds ptnDocIds)
        {

            List<ValueTextBool> retValue = new List<ValueTextBool>();

            DemographicsAssociatedDoctor currDoctor = (from pt in context.Demographics
                                                       join fdoc in context.DemographicsAssociatedDoctors on pt.Id equals fdoc.DemographicId
                                                       join ex in context.ExternalDoctors on fdoc.ExternalDoctorId equals ex.Id
                                                       where pt.PatientRecordId == ptnDocIds.PatientRecordId &&
                                                       fdoc.ExternalDoctorId == ptnDocIds.ExternalDoctorId &&
                                                       fdoc.IsRemoved == false
                                                       select fdoc).FirstOrDefault();

            if (currDoctor != null)
            {
                currDoctor.IsRemoved = !currDoctor.IsRemoved;
                context.Entry(currDoctor).State = EntityState.Modified;

                context.SaveChanges();
            }



            List<DoctorsData> docList = (from pt in context.Demographics
                                         join fdoc in context.DemographicsAssociatedDoctors on pt.Id equals fdoc.DemographicId
                                         join ex in context.ExternalDoctors on fdoc.ExternalDoctorId equals ex.Id
                                         where pt.PatientRecordId == ptnDocIds.PatientRecordId &&
                                         fdoc.IsRemoved == false
                                         orderby fdoc.Id descending
                                         select new DoctorsData
                                         {
                                             text = (ex.lastName ?? "") + ", " + (ex.firstName ?? ""),
                                             Id = ex.Id,
                                             isActive = fdoc.IsActive,
                                             isRemoved = fdoc.IsRemoved
                                         }).ToList();

            if (docList != null && docList.Count > 0)
            {
                if (docList.Count == 1)
                {
                    retValue = docList.Select(t => new ValueTextBool()
                    { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();
                }
                else
                {
                    DoctorsData primeDoc = docList.Where(t => t.isActive == true).FirstOrDefault();
                    if (primeDoc != null)
                    {
                        docList.Remove(primeDoc);
                        List<DoctorsData> tempList = new List<DoctorsData>() { primeDoc };
                        tempList.AddRange(docList);
                        retValue = tempList.Select(t => new ValueTextBool()
                        { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();

                    }
                    else
                    {
                        retValue = docList.Select(t => new ValueTextBool()
                        { isActive = t.isActive, Id = t.Id, text = t.text, isRemoved = t.isRemoved }).ToList();
                    }
                }
            }

            return retValue;
        }

        public ContactQuery AddNewContact(contactToSend contact, int userid, string ipaddress)
        {
            ContactQuery retVal = new ContactQuery();

            int demographicId = context.Demographics.
                    Where(t => t.PatientRecordId == contact.PatientRecordId).Select(tt => tt.Id).FirstOrDefault();

            //change the prime contact
            if (contact.active)
            {
                List<DemographicsNextOfKin> testPrimeKeenList = context.DemographicsContacts.
                                Where(t => t.DemographicId == demographicId && t.IsActive == true).ToList();

                foreach (var item in testPrimeKeenList)
                {
                    item.IsActive = false;

                    context.Entry(item).State = EntityState.Modified;
                }
            }

            DemographicsNextOfKin nextKeen = new DemographicsNextOfKin();
            nextKeen.ContactPurposeEnum = (ContactPurpose)contact.ContactPurposeId;
            if ((ContactPurpose)contact.ContactPurposeId == ContactPurpose.O)
            {
                nextKeen.contactPurpose = "Other";
            }
            nextKeen.firstName = contact.firstName ?? "";
            nextKeen.lastName = contact.lastName ?? "";
            nextKeen.emailAddress = contact.email ?? "";
            nextKeen.notes = contact.notes ?? "";
            nextKeen.DemographicId = demographicId;
            nextKeen.IsActive = contact.active;
            nextKeen.IsRemoved = contact.removed;
            nextKeen.CreatedDateTime = DateTime.Now;


            context.Entry(nextKeen).State = EntityState.Added;

            try
            {
                context.SaveChanges(userid, ipaddress);

                #region Add phone
                if (contact.phoneNumber != null && contact.phoneNumber != "")
                {
                    DemographicsContactPhoneNumber ph = new DemographicsContactPhoneNumber();
                    ph.contactPhoneNumber = contact.phoneNumber;
                    ph.extention = contact.phoneExtention;
                    ph.typeOfPhoneNumber = (PhoneNumberType)contact.PhoneTypeId;
                    ph.IsActive = contact.activePh;
                    ph.IsRemoved = false;
                    ph.DemographicsContactId = nextKeen.Id;

                    context.Entry(ph).State = EntityState.Added;
                    context.SaveChanges(userid, ipaddress);
                }
                #endregion

                retVal = GetDemographicsContactList(retVal, demographicId);

                retVal.message = "New contact created successfully!";
                retVal.success = true;
            }
            catch (Exception ex)
            {
                #region exep
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### AddNewContact";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message;

                _log.Error(Msg);
                //UtilityHelper.WriteToLog(Msg);

                retVal.message = "There was a problem to entre new contact. Contact Administrator! ";
                retVal.success = false;
                #endregion
            }

            return retVal;
        }

        public ContactQuery EditContact(contactToSend contact, int userid, string ipaddress)
        {
            ContactQuery retVal = new ContactQuery();

            int demographicId = context.Demographics.
                    Where(t => t.PatientRecordId == contact.PatientRecordId).Select(tt => tt.Id).FirstOrDefault();
            if (demographicId == 0)
            {
                retVal.message = "Patient is missing!.";
                retVal.success = false;
                return retVal;
            }

            DemographicsNextOfKin testKeen = context.DemographicsContacts.
                Where(t => t.Id == contact.contactIdForSend).FirstOrDefault();
            if (testKeen == null)
            {
                retVal.message = "This Next of Keen is missing in database.";
                retVal.success = false;
                return retVal;
            }

            //change the prime contact
            if (contact.active)
            {
                List<DemographicsNextOfKin> testPrimeKeenList = context.DemographicsContacts.
                                Where(t => t.DemographicId == demographicId && t.IsActive == true).ToList();

                foreach (var item in testPrimeKeenList)
                {
                    if (item.Id != testKeen.Id)
                    {
                        item.IsActive = false;
                        context.Entry(item).State = EntityState.Modified;
                    }


                }
            }


            testKeen.ContactPurposeEnum = (ContactPurpose)contact.ContactPurposeId;
            //if ((ContactPurpose)contact.ContactPurposeId == ContactPurpose.O)
            //{
            //    testKeen.contactPurpose = "Other";
            //}
            //else
            //{
            //    testKeen.contactPurpose = null;
            //}
            testKeen.firstName = contact.firstName ?? "";
            testKeen.lastName = contact.lastName ?? "";
            testKeen.emailAddress = contact.email ?? "";
            testKeen.notes = contact.notes ?? "";
            testKeen.IsActive = contact.active;
            testKeen.IsRemoved = contact.removed;

            context.Entry(testKeen).State = EntityState.Modified;

            #region Update phone
            if (contact.phoneNumber != null && contact.phoneNumber != "")
            {
                DemographicsContactPhoneNumber ph = context.DemographicsContactPhoneNumbers.
                    Where(t => t.DemographicsContactId == testKeen.Id && t.contactPhoneNumber == contact.phoneNumber).FirstOrDefault();
                if (ph != null)
                {
                    //change the prime contact phone
                    if (contact.activePh)
                    {
                        List<DemographicsContactPhoneNumber> PhList = (from dmg in context.DemographicsContactPhoneNumbers
                                                                       join dcon in context.DemographicsContacts on dmg.DemographicsContactId equals dcon.Id
                                                                       where dcon.Id == contact.contactIdForSend &&
                                                                       dmg.IsActive == true
                                                                       select dmg).ToList();

                        foreach (var item in PhList)
                        {
                            if (item.Id != contact.phoneIdForSend)
                            {
                                item.IsActive = false;
                                context.Entry(item).State = EntityState.Modified;
                            }
                        }
                    }


                    ph.typeOfPhoneNumber = (PhoneNumberType)contact.PhoneTypeId;
                    ph.extention = contact.phoneExtention;
                    ph.IsActive = contact.activePh;
                    ph.IsRemoved = false;

                    context.Entry(ph).State = EntityState.Modified;
                }
            }
            #endregion

            try
            {
                context.SaveChanges(userid, ipaddress);

                retVal = GetDemographicsContactList(retVal, demographicId);

                retVal.message = "New contact created successfully!";
                retVal.success = true;
            }
            catch (Exception ex)
            {
                string Msg = $" ### AddNewContact {ex.ExceptionDetails()}";
                _log.Error(Msg);

                retVal.message = "There was a problem to entre new contact. Contact Administrator! ";
                retVal.success = false;
            }

            return retVal;
        }

        public ContactQuery EditContactPhone(contactToSend contact, int userid, string ipaddress)
        {
            ContactQuery retVal = new ContactQuery();

            int demographicId = context.Demographics.
                    Where(t => t.PatientRecordId == contact.PatientRecordId).Select(tt => tt.Id).FirstOrDefault();
            if (demographicId == 0)
            {
                retVal.message = "Patient is missing!.";
                retVal.success = false;
                return retVal;
            }

            DemographicsContactPhoneNumber testPh = context.DemographicsContactPhoneNumbers.
                Where(t => t.DemographicsContactId == contact.contactIdForSend && t.Id == contact.phoneIdForSend).FirstOrDefault();
            if (testPh == null)
            {
                retVal.message = "Phone for the Next of Keen is missing in database.";
                retVal.success = false;
                return retVal;
            }

            //change the prime contact phone
            if (contact.activePh)
            {
                List<DemographicsContactPhoneNumber> PhList = (from dmg in context.DemographicsContactPhoneNumbers
                                                               join dcon in context.DemographicsContacts on dmg.DemographicsContactId equals dcon.Id
                                                               where dcon.Id == contact.contactIdForSend &&
                                                               dmg.IsActive == true
                                                               select dmg).ToList();

                foreach (var item in PhList)
                {
                    if (item.Id != testPh.Id)
                    {
                        item.IsActive = false;
                        context.Entry(item).State = EntityState.Modified;
                    }
                }
            }

            testPh.typeOfPhoneNumber = (PhoneNumberType)contact.PhoneTypeId;
            testPh.extention = contact.phoneExtention;
            testPh.IsActive = contact.activePh;
            testPh.IsRemoved = false;
            testPh.DemographicsContactId = contact.contactIdForSend;
            testPh.contactPhoneNumber = contact.phoneNumber;

            context.Entry(testPh).State = EntityState.Modified;

            try
            {
                context.SaveChanges(userid, ipaddress);

                retVal = GetDemographicsContactList(retVal, demographicId);

                retVal.message = "New contact phone updated successfully!";
                retVal.success = true;
            }
            catch (Exception ex)
            {
                string Msg = $" ### EditContactPhone {ex.ExceptionDetails()}";
                _log.Error(Msg);
                retVal.message = "There was a problem to update contact phone. Contact Administrator! ";
                retVal.success = false;
            }

            return retVal;
        }

        public ContactQuery NewContactPhone(contactToSend contact, int userid, string ipaddress)
        {
            ContactQuery retVal = new ContactQuery();

            DemographicsNextOfKin testKeen = context.DemographicsContacts.
                Where(t => t.Id == contact.contactIdForSend).FirstOrDefault();
            if (testKeen == null)
            {
                retVal.message = "This Next of Keen is missing in database.";
                retVal.success = false;
                return retVal;
            }

            if (contact.phoneNumber == null || contact.phoneNumber == "")
            {
                retVal.message = "Phone numbre can not be empty!";
                retVal.success = false;
                return retVal;
            }

            //change the prime contact phone
            if (contact.activePh)
            {
                List<DemographicsContactPhoneNumber> PhList = (from dmg in context.DemographicsContactPhoneNumbers
                                                               join dcon in context.DemographicsContacts on dmg.DemographicsContactId equals dcon.Id
                                                               where dcon.Id == contact.contactIdForSend &&
                                                               dmg.IsActive == true
                                                               select dmg).ToList();

                foreach (var item in PhList)
                {
                    item.IsActive = false;
                    context.Entry(item).State = EntityState.Modified;
                }
            }


            DemographicsContactPhoneNumber ph = new DemographicsContactPhoneNumber();
            ph.typeOfPhoneNumber = (PhoneNumberType)contact.PhoneTypeId;
            ph.IsActive = contact.activePh;
            ph.extention = contact.phoneExtention;
            ph.IsRemoved = false;
            ph.DemographicsContactId = testKeen.Id;
            ph.contactPhoneNumber = contact.phoneNumber;

            context.Entry(ph).State = EntityState.Added;

            try
            {
                context.SaveChanges(userid, ipaddress);

                retVal = GetDemographicsContactList(retVal, testKeen.DemographicId);

                retVal.message = "New contact phone created successfully!";
                retVal.success = true;
            }
            catch (Exception ex)
            {
                string Msg = $" ### NewContactPhone {ex.ExceptionDetails()}";
                _log.Error(Msg);

                retVal.message = "There was a problem to entre new contact phone. Contact Administrator! ";
                retVal.success = false;
            }

            return retVal;
        }

        private ContactQuery GetDemographicsContactList(ContactQuery retVal, int demographicId)
        {
            #region Create List of
            List<name_id_nkx> list = (from demCont in context.DemographicsContacts
                                      where demCont.DemographicId == demographicId
                                      select new name_id_nkx
                                      {
                                          id = demCont.Id,
                                          name = (demCont.lastName ?? "") + ", " + (demCont.firstName ?? ""),
                                          phoneNumbers = (context.DemographicsContactPhoneNumbers.
                                                    Where(t => t.DemographicsContactId == demCont.Id && t.IsRemoved == false).
                                                    OrderByDescending(tt => tt.Id).ToList()),
                                          phone = "",
                                          phoneType = "",
                                          contactPurpose = demCont.contactPurpose ?? "",
                                          purposeEnum = demCont.ContactPurposeEnum,
                                          purposeEnumId = (int)demCont.ContactPurposeEnum,
                                          IsActive = demCont.IsActive,
                                          isRemoved = demCont.IsRemoved,
                                          FName = (demCont.firstName ?? ""),
                                          LName = (demCont.lastName ?? ""),
                                          emailAddress = (demCont.emailAddress ?? ""),
                                          notes = (demCont.notes ?? "")
                                      }).ToList();
            if (list != null && list.Count > 0)
            {
                List<name_id_nkx> removedList = new List<name_id_nkx>();

                foreach (var item in list)
                {


                    //filter deleted
                    if (item.isRemoved == true)
                    {
                        removedList.Add(item);
                        continue;
                    }

                    if (item.purposeEnum == ContactPurpose.O)
                    {
                        item.purpose = (item.contactPurpose == null || item.contactPurpose == "") ? "Other" : item.contactPurpose;
                    }
                    else
                    {
                        item.purpose = EnumExtensions.GetDiscriptionName(item.purposeEnum);
                        item.purpose = item.purpose == "Select..." ? "" : item.purpose;
                    }

                    //item.purpose = EnumExtensions.GetDiscriptionName(item.purposeEnum);
                    //item.purpose = item.purpose == "Select..." ? "" : item.purpose;

                    if (item.phoneNumbers != null && item.phoneNumbers.Count > 0)
                    {
                        DemographicsContactPhoneNumber ph_mun_prime = item.phoneNumbers.Where(t => t.IsActive == true).FirstOrDefault();
                        if (ph_mun_prime != null)
                        {
                            item.phone = ph_mun_prime.contactPhoneNumber ?? "";
                            item.phoneExt = ph_mun_prime.extention ?? "";
                            item.phoneType = EnumExtensions.GetDiscriptionName(ph_mun_prime.typeOfPhoneNumber);
                            item.phoneId = ph_mun_prime.Id;
                            item.phoneTypeEnumId = (int)ph_mun_prime.typeOfPhoneNumber;
                        }
                        else
                        {
                            DemographicsContactPhoneNumber ph_mun = item.phoneNumbers.FirstOrDefault();
                            if (ph_mun != null)
                            {
                                item.phone = ph_mun.contactPhoneNumber ?? "";
                                item.phoneExt = ph_mun.extention ?? "";
                                item.phoneType = EnumExtensions.GetDiscriptionName(ph_mun.typeOfPhoneNumber);
                                item.phoneId = ph_mun.Id;
                                item.phoneTypeEnumId = (int)ph_mun.typeOfPhoneNumber;
                            }
                        }

                        item.stringOfPhNumbers = CreateListOfPhAsString(item.phoneNumbers, item.id, item.name);

                        item.phoneNumbers = null;
                    }
                    else
                    {
                        item.stringOfPhNumbers = "-";
                    }
                }

                if (removedList.Count > 0)
                {
                    List<int> Ids1 = removedList.Select(t => t.id).ToList();
                    list.RemoveAll(t => Ids1.Contains(t.id));
                }

                list = list.OrderByDescending(t => t.id).ToList();
                name_id_nkx itemActive = list.Where(t => t.IsActive == true && t.isRemoved == false).FirstOrDefault();
                if (itemActive != null)
                {
                    list.Remove(itemActive);
                    List<name_id_nkx> tempList = new List<name_id_nkx>() { itemActive };
                    tempList.AddRange(list);
                    list = tempList;
                }


                #region format the list
                if (list.Count == 1)
                {
                    retVal.contactList = list.Select(t => new contactToSend()
                    {
                        active = t.IsActive,
                        id = t.id,
                        ContactPurposeId = (int)t.purposeEnum,
                        ContactPurposeStr = t.purpose,
                        email = t.emailAddress,
                        firstName = t.FName,
                        lastName = t.LName,
                        notes = t.notes,
                        phoneIdForSend = t.phoneId,
                        phoneNumber = t.phone + ((t.phoneExt != null && t.phoneExt != "") ? " ext " + t.phoneExt : ""),
                        phoneExtention = t.phoneExt,
                        PhoneTypeId = t.phoneTypeEnumId,
                        PhoneTypeStr = t.phoneType,
                        phoneId = t.phoneId,
                        isAllHidden = t.stringOfPhNumbers == "-" ? "hidden" : "-",
                        stringOfPhNumbers = t.stringOfPhNumbers
                    }).ToList();
                }
                else
                {
                    name_id_nkx primeCont = list.Where(t => t.IsActive == true).FirstOrDefault();
                    if (primeCont != null)
                    {
                        list.Remove(primeCont);
                        List<name_id_nkx> tempList = new List<name_id_nkx>() { primeCont };
                        tempList.AddRange(list);
                        retVal.contactList = tempList.Select(t => new contactToSend()
                        {
                            active = t.IsActive,
                            id = t.id,
                            ContactPurposeId = (int)t.purposeEnum,
                            ContactPurposeStr = t.purpose,
                            email = t.emailAddress,
                            firstName = t.FName,
                            lastName = t.LName,
                            notes = t.notes,
                            phoneIdForSend = t.phoneId,
                            phoneNumber = t.phone + ((t.phoneExt != null && t.phoneExt != "") ? " ext " + t.phoneExt : ""),
                            phoneExtention = t.phoneExt,
                            PhoneTypeId = t.phoneTypeEnumId,
                            PhoneTypeStr = t.phoneType,
                            phoneId = t.phoneId,
                            isAllHidden = t.stringOfPhNumbers == "-" ? "hidden" : "-",
                            stringOfPhNumbers = t.stringOfPhNumbers
                        }).ToList();

                    }
                    else
                    {
                        retVal.contactList = list.Select(t => new contactToSend()
                        {
                            active = t.IsActive,
                            id = t.id,
                            ContactPurposeId = (int)t.purposeEnum,
                            ContactPurposeStr = t.purpose,
                            email = t.emailAddress,
                            firstName = t.FName,
                            lastName = t.LName,
                            notes = t.notes,
                            phoneIdForSend = t.phoneId,
                            phoneNumber = t.phone + ((t.phoneExt != null && t.phoneExt != "") ? " ext " + t.phoneExt : ""),
                            phoneExtention = t.phoneExt,
                            PhoneTypeId = t.phoneTypeEnumId,
                            PhoneTypeStr = t.phoneType,
                            phoneId = t.phoneId,
                            isAllHidden = t.stringOfPhNumbers == "-" ? "hidden" : "-",
                            stringOfPhNumbers = t.stringOfPhNumbers
                        }).ToList();
                    }
                }
                #endregion
            }
            #endregion

            return retVal;
        }

        public mrnToSend UpdatePatientsMRN(mrnToSend mrnInfo, int userid, string ipaddress)
        {
            mrnToSend retVal = new mrnToSend();

            if (mrnInfo.patientRecordId == 0)
            {
                retVal.message = "Patient is missing!.";
                retVal.success = false;
                return retVal;
            }
            if (mrnInfo.hospitalId == 0)
            {
                retVal.message = "Hospital is missing!.";
                retVal.success = false;
                return retVal;
            }

            Hospital hosp = context.Hospitals.
                Where(t => t.Id == mrnInfo.hospitalId).FirstOrDefault();
            if (hosp == null)
            {
                retVal.message = "Hospital is wrong!.";
                retVal.success = false;
                return retVal;
            }

            PatientMRN mrnC = context.PatientMRNs.
                            Where(t => t.HospitalId == mrnInfo.hospitalId && t.PatientRecordId == mrnInfo.patientRecordId).FirstOrDefault();
            if (mrnC == null)
            {
                PatientMRN mrn = new PatientMRN();
                mrn.HospitalId = mrnInfo.hospitalId;
                mrn.MedicalRecordNumber = mrnInfo.mrn;
                mrn.PatientRecordId = mrnInfo.patientRecordId;

                context.Entry(mrn).State = EntityState.Added;
            }
            else
            {
                mrnC.MedicalRecordNumber = mrnInfo.mrn;

                context.Entry(mrnC).State = EntityState.Modified;
            }

            try
            {
                context.SaveChanges(userid, ipaddress);

                retVal.message = "MRN updated successfully!";
                retVal.success = true;
            }
            catch (Exception ex)
            {
                string Msg = $" ### UpdatePatientsMRN {ex.ExceptionDetails()}";

                _log.Error(Msg);

                retVal.message = "There was a problem to update mrn. Contact Administrator! ";
                retVal.success = false;
            }

            return retVal;
        }
        public bool IsPatientHasAlert(int patientId)
        {
            var dbAlert = context.PatientAlerts.Where(p => p.PatientRecordId == patientId
                                                    && p.IsActive && !p.IsDeleted).FirstOrDefault();

            if (dbAlert == null)
            {
                return false;
            }
            if (!dbAlert.EndDate.HasValue || (dbAlert.EndDate.HasValue && ((DateTime)dbAlert.EndDate.Value).Date >= DateTime.Now.Date))
            {
                return true;
            }
            return false;
        }
        #region Patient Phons
        public PhoneQuery AddNewPPhone(PhoneDataTransfer phInfo, int userid, string ipaddress)
        {
            List<PhoneDataTransfer> retList = new List<PhoneDataTransfer>();
            PhoneQuery retVal = new PhoneQuery();

            if (phInfo.patientRecordId == 0)
            {
                retVal.message = "Patient is missing!.";
                retVal.success = false;
                return retVal;
            }
            if (phInfo.phone == null || phInfo.phone == "")
            {
                retVal.message = "Phone number is missing!.";
                retVal.success = false;
                return retVal;
            }

            int demographicId = context.Demographics.Where(t => t.PatientRecordId == phInfo.patientRecordId).
                Select(tt => tt.Id).FirstOrDefault();

            DemographicsPhoneNumber phn = context.PhoneNumber.
                Where(t => t.phoneNumber == phInfo.phone && t.DemographicId == demographicId).FirstOrDefault();
            if (phn != null)
            {
                if (!phn.IsRemoved)
                {
                    retVal.message = "Patient already has this phone number!.";
                    retVal.success = false;
                    return retVal;
                }
            }
            else
            {
                phn = new DemographicsPhoneNumber();
                context.Entry(phn).State = EntityState.Added;
            }

            if (phInfo.isActive)
            {
                List<DemographicsPhoneNumber> phnActiveList = context.PhoneNumber.
                            Where(t => t.IsActive == true && t.DemographicId == demographicId).ToList();
                foreach (var item in phnActiveList)
                {
                    item.IsActive = false;
                    context.Entry(item).State = EntityState.Modified;
                }
            }

            phn.phoneNumber = phInfo.phone;
            phn.typeOfPhoneNumber = (PhoneNumberType)phInfo.phoneNumberTypeId;
            phn.extention = phInfo.ext;
            phn.DemographicId = demographicId;
            phn.notes = phInfo.note;
            phn.IsActive = phInfo.isActive;
            phn.IsRemoved = phInfo.isRemoved;

            try
            {
                context.SaveChanges(userid, ipaddress);

                List<DemographicsPhoneNumber> list = (from dmg in context.Demographics
                                                      join pr in context.PatientRecords on dmg.PatientRecordId equals pr.Id
                                                      join dph in context.PhoneNumber on dmg.Id equals dph.DemographicId
                                                      where dmg.PatientRecordId == phInfo.patientRecordId && dph.IsRemoved == false
                                                      orderby dph.Id descending
                                                      select dph).ToList();


                if (list != null && list.Count > 0)
                {
                    retList = GetPhoneListFormated(list);

                }

                retVal.contactList = retList;
                retVal.message = "Patient phone added successfully!";
                retVal.success = true;
            }
            catch (Exception ex)
            {
                string Msg = $" ### AddNewPPhone {ex.ExceptionDetails()}";
                _log.Error(Msg);

                retVal.message = "There was a problem to add phone number. Contact Administrator! ";
                retVal.success = false;
            }

            return retVal;
        }

        public PhoneQuery EditPPhone(PhoneDataTransfer phInfo, int userid, string ipaddress)
        {
            List<PhoneDataTransfer> retList = new List<PhoneDataTransfer>();
            PhoneQuery retVal = new PhoneQuery();

            if (phInfo.patientRecordId == 0)
            {
                retVal.message = "Patient is missing!";
                retVal.success = false;
                return retVal;
            }
            if (phInfo.phone == null || phInfo.phone == "")
            {
                retVal.message = "Phone number is missing!";
                retVal.success = false;
                return retVal;
            }
            int demographicId = context.Demographics.Where(t => t.PatientRecordId == phInfo.patientRecordId).
                Select(tt => tt.Id).FirstOrDefault();

            DemographicsPhoneNumber phn = context.PhoneNumber.
                Where(t => t.Id == phInfo.phoneId && t.DemographicId == demographicId).FirstOrDefault();
            if (phn == null)
            {
                retVal.message = "Patient phone missing in db!";
                retVal.success = false;
                return retVal;
            }

            if (phInfo.isActive)
            {
                List<DemographicsPhoneNumber> phnActiveList = context.PhoneNumber.
                            Where(t => t.IsActive == true && t.DemographicId == demographicId).ToList();
                foreach (var item in phnActiveList)
                {
                    if (item.Id != phInfo.phoneId)
                    {
                        item.IsActive = false;
                        context.Entry(item).State = EntityState.Modified;
                    }
                }
            }

            phn.phoneNumber = phInfo.phone;
            phn.notes = phInfo.note;
            phn.typeOfPhoneNumber = (PhoneNumberType)phInfo.phoneNumberTypeId;
            phn.extention = phInfo.ext;
            phn.IsActive = phInfo.isActive;
            phn.IsRemoved = phInfo.isRemoved;

            context.Entry(phn).State = EntityState.Modified;

            try
            {
                context.SaveChanges(userid, ipaddress);

                List<DemographicsPhoneNumber> list = (from dmg in context.Demographics
                                                      join pr in context.PatientRecords on dmg.PatientRecordId equals pr.Id
                                                      join dph in context.PhoneNumber on dmg.Id equals dph.DemographicId
                                                      where dmg.PatientRecordId == phInfo.patientRecordId && dph.IsRemoved == false
                                                      orderby dph.Id descending
                                                      select dph).ToList();


                if (list != null && list.Count > 0)
                {
                    retList = GetPhoneListFormated(list);

                }

                retVal.contactList = retList;
                retVal.message = "Patient phone updated successfully!";
                retVal.success = true;
            }
            catch (Exception ex)
            {
                string Msg = $" ### EditPPhone {ex.ExceptionDetails()}";
                _log.Error(Msg);
                retVal.message = "There was a problem to update phone. Contact Administrator! ";
                retVal.success = false;
            }

            return retVal;
        }
        public VMPatient GetPatientInfoForMed(int patientId)
        {
            VMPatient patient = null;

            var demographics = GetDemographic(patientId);
            if (demographics != null)
            {
                patient = demographics.ToVmPatient();
            }

            return patient;
        }
        public VMReportPatient GetReportPatient(int patientId)
        {
            VMReportPatient patient = null;

            //get the first just like demographics edit patient untill we fix
            var demographics = GetDemographic(patientId);

            if (demographics != null)
            {
                patient = demographics.ToVmReportPatient();
            }

            return patient;
        }

        private Demographic GetDemographic(int patientRecordId)
        {
            var demographics = context.Demographics
               .Include("PatientRecord")
               .Include("addresses")
               .Include("phoneNumbers")
               .Include("healthcards")
               .Where(p => p.PatientRecordId == patientRecordId)
               .FirstOrDefault();

            return demographics;
        }

        private List<PhoneDataTransfer> GetPhoneList_n(List<DemographicsPhoneNumber> list)
        {
            List<PhoneDataTransfer> retList = new List<PhoneDataTransfer>();

            if (list != null && list.Count > 0)
            {
                list = list.Where(t => t.IsRemoved == false).ToList();
                retList = GetPhoneListFormated(list);

                return retList;
            }
            else
            {
                return retList;
            }
        }

        public List<PhoneDataTransfer> GetPhoneList(int patientRecordId)
        {
            List<PhoneDataTransfer> retList = new List<PhoneDataTransfer>();

            List<DemographicsPhoneNumber> list = (from dmg in context.Demographics
                                                  join pr in context.PatientRecords on dmg.PatientRecordId equals pr.Id
                                                  join dph in context.PhoneNumber on dmg.Id equals dph.DemographicId
                                                  where dmg.PatientRecordId == patientRecordId && dph.IsRemoved == false
                                                  orderby dph.Id descending
                                                  select dph).ToList();


            if (list != null && list.Count > 0)
            {
                retList = GetPhoneListFormated(list);

                return retList;
            }
            else
            {
                return retList;
            }
        }

        private List<PhoneDataTransfer> GetPhoneListFormated(List<DemographicsPhoneNumber> list)
        {
            List<PhoneDataTransfer> retList = new List<PhoneDataTransfer>();
            #region Create List of
            foreach (var item in list)
            {
                PhoneDataTransfer newItem = new PhoneDataTransfer();
                newItem.phone = item.phoneNumber ?? "";
                newItem.note = item.notes ?? "";
                newItem.ext = item.extention ?? "";
                newItem.phoneNumberTypeId = (int)item.typeOfPhoneNumber;
                newItem.phoneType = EnumExtensions.GetDiscriptionName(item.typeOfPhoneNumber);
                newItem.phoneId = item.Id;
                newItem.isActive = item.IsActive;
                newItem.isRemoved = item.IsRemoved;

                retList.Add(newItem);
            }


            PhoneDataTransfer itemActive = retList.Where(t => t.isActive == true && t.isRemoved == false).FirstOrDefault();
            if (itemActive != null)
            {
                retList.Remove(itemActive);
                List<PhoneDataTransfer> tempList = new List<PhoneDataTransfer>() { itemActive };
                tempList.AddRange(retList);
                retList = tempList;
            }
            #endregion

            return retList;
        }
        #endregion

        #region Cohorts
        public string DeletePatientCohort(int patCohId_int)
        {
            string ret = "";

            try
            {
                PatientCohort p_cohort = context.PatientCohorts.
                Where(t => t.Id == patCohId_int).FirstOrDefault();
                if (p_cohort != null)
                {
                    context.Entry(p_cohort).State = EntityState.Deleted;
                    context.SaveChanges();
                    ret = patCohId_int.ToString();
                }
            }
            catch (Exception ex)
            {
                string Msg = $" ### RepositoryForAdminUser.UpdateCohortPatinet {ex.ExceptionDetails()}";
                _log.Error(Msg);
                ret = "";
            }

            return ret;
        }

        public VM_DemCohort GetDemCohort(VM_DemCohort demModel)
        {
            SelectList selectCohorts = GetSelectListCohorts(demModel.practiceId);
            //demModel.cohortsList_1 = selectCohorts ?? (new SelectList(new List<SelectListItem>() { new SelectListItem() { Text = "", Value = "" } }));
            demModel.cohortsList_1 = new SelectList(new List<SelectListItem>() { new SelectListItem() { Text = "", Value = "" } });
            demModel.SelectedCohort_1_Id = "0";

            List<ValueText1> checkedCohorts = GetCohortsByPatientRecordId(demModel.coh_patientRecordId, demModel.practiceId);
            demModel.addedCohorts = checkedCohorts;
            //SelectList selectDoctors = GetSelectListDoctors(demModel.practiceId);
            AppointmentsBLL scheduleBLL = new AppointmentsBLL();
            var practiceDoctors = scheduleBLL.GetPracticeDoctors(demModel.practiceId);
            if (practiceDoctors == null || practiceDoctors.Count == 0)
                demModel.doctorsList_1 = new SelectList(new List<SelectListItem>() { new SelectListItem() { Text = "", Value = "" } });
            else
            {
                IEnumerable<SelectListItem> selectListItems = null;
                SelectList selectList = null;
                //List<SelectListItem> ListItems_ = new List<SelectListItem>() { new SelectListItem { Text = "All", Value = "0" } };
                List<SelectListItem> ListItems_ = new List<SelectListItem>();
                selectListItems = practiceDoctors.Select(t => new SelectListItem { Text = (t.LastName ?? "") + ", " + (t.FirstName ?? ""), Value = t.Id.ToString() });
                ListItems_.AddRange(selectListItems);
                selectList = new SelectList(ListItems_.AsEnumerable(), "Value", "Text");
                demModel.doctorsList_1 = selectList;
            }
            //demModel.doctorsList_1 = selectDoctors ?? (new SelectList(new List<SelectListItem>() { new SelectListItem() { Text = "", Value = "" } }));
            demModel.SelectedDoctor_1_Id = "0";

            demModel.coh_name = GetPatientNameForCohort(demModel.coh_patientRecordId);
            demModel.dateStarted = "";
            demModel.dateTerminated = "";


            return demModel;
        }

        public Cerebrum.ViewModels.Cohort.PatientCohortVM GetDemCohort(int patientRecordId, int practiceId)
        {
            Cerebrum.ViewModels.Cohort.PatientCohortVM patientCohort = new Cerebrum.ViewModels.Cohort.PatientCohortVM();
            patientCohort.patientRecordId = patientRecordId;
            if (patientRecordId == 0)
                patientCohort.errorMessage = "Patient is not selected!";
            else
            {
                patientCohort.patientName = GetPatientNameForCohort(patientRecordId);
                AppointmentsBLL scheduleBLL = new AppointmentsBLL();
                var practiceDoctors = scheduleBLL.GetPracticeDoctors(practiceId);
                patientCohort.PracticeDoctors = practiceDoctors.Select(e => new Cerebrum.ViewModels.Cohort.TextValueViewModel { text = (e.LastName ?? string.Empty) + ", " + (e.FirstName ?? string.Empty), value = e.PracticeDoctorId.ToString() }).ToList();
            }

            return patientCohort;
        }

        public Cerebrum.ViewModels.Cohort.PatientCohortVM GetCohortClassByDoctor(int practiceDoctorId, int practiceId)
        {
            Cerebrum.ViewModels.Cohort.PatientCohortVM patientCohort = new Cerebrum.ViewModels.Cohort.PatientCohortVM();
            var cohortClasses = context.CohortClasses.Where(a => a.PracticeId == practiceId && (a.PracticeDoctorId == 0 || a.PracticeDoctorId == practiceDoctorId)).ToList();
            patientCohort.CohortClasses = cohortClasses.Select(e => new Cerebrum.ViewModels.Cohort.TextValueViewModel { text = e.Description, value = e.Id.ToString() }).ToList();
            return patientCohort;
        }

        public VM_DemCohort SaveDemCohort(VM_DemCohort demModel, int userid, string ipaddress)
        {
            //RepositoryForAdminUser rep = new RepositoryForAdminUser();
            int patientId_int = demModel.coh_patientRecordId;
            int cohortId_int = int.Parse(demModel.SelectedCohort_1_Id);
            string cohName = context.Cohorts.Where(t => t.Id == cohortId_int).Select(t => t.Description).FirstOrDefault() ?? "-";

            string ret = SavePatientCohortNew(patientId_int, cohortId_int, demModel.dateStarted,
                demModel.dateTerminated, demModel.SelectedDoctor_1_Id, demModel.Notes, userid, ipaddress);

            demModel.pat_cohort_Id = ret;
            if (ret != "")
            {
                demModel.success = true;
                demModel.message = "Cohort added successfully!";
                demModel.coh_name = cohName;
            }
            else
            {
                demModel.success = false;
                demModel.message = "There was problem to add cohort!";
            }

            return demModel;
        }

        public SelectList GetSelectListDoctors(int practiceId)
        {
            IEnumerable<SelectListItem> selectListItems = null;
            SelectList selectList = null;
            List<SelectListItem> ListItems_ = new List<SelectListItem>() { new SelectListItem { Text = "All", Value = "0" } };

            try
            {
                List<ValueDisplay_n> doctorSet = (from pd in context.PracticeDoctors
                                                  join exd in context.ExternalDoctors on pd.ExternalDoctorId equals exd.Id
                                                  where pd.PracticeId == practiceId
                                                  select new ValueDisplay_n
                                                  {
                                                      display = (exd.lastName ?? "") + ", " + (exd.firstName ?? ""),
                                                      value = pd.Id.ToString()
                                                  }).ToList();

                var firstCode = doctorSet.Select(t => t.value).FirstOrDefault();
                if (firstCode != null)
                {
                    //selectListItems = doctorSet.Select(t => new SelectListItem { Text = t.lastName + ", " + t.firstName, Value = t.Id.ToString() });
                    selectListItems = doctorSet.Select(t => new SelectListItem { Text = t.display, Value = t.value });
                    ListItems_.AddRange(selectListItems);
                    selectList = new SelectList(ListItems_.AsEnumerable(), "Value", "Text");
                }
                else
                {
                    selectList = new SelectList(ListItems_.AsEnumerable(), "Value", "Text");
                }
            }
            catch (Exception ex)
            {
                string Msg = $" ### RepositoryForAdminUser.GetSelectListOffices {ex.ExceptionDetails()}";

                _log.Error(Msg);
                selectList = new SelectList(ListItems_.AsEnumerable(), "Value", "Text");
                return selectList;
            }

            return selectList;
        }

        private SelectList GetSelectListCohorts(int practiceId)
        {
            IEnumerable<SelectListItem> selectListItems = null;
            SelectList selectList = null;
            List<SelectListItem> ListItems_ = new List<SelectListItem>() { new SelectListItem { Text = "Select Cohort", Value = "0" } };

            try
            {
                List<Cerebrum.Data.Cohort> officeSet = (from exd in context.Cohorts
                                                        where exd.practiceId == practiceId
                                                        select exd).ToList();

                int firstCode = officeSet.Select(t => t.Id).FirstOrDefault();
                if (firstCode != 0)
                {
                    selectListItems = officeSet.Select(t => new SelectListItem { Text = t.Description, Value = t.Id.ToString() });
                    ListItems_.AddRange(selectListItems);
                    selectList = new SelectList(ListItems_.AsEnumerable(), "Value", "Text");
                }
                else
                {
                    selectList = new SelectList(ListItems_.AsEnumerable(), "Value", "Text");
                }
            }
            catch (Exception ex)
            {
                string Msg = $" ### RepositoryForAdminUser.GetSelectListCohorts {ex.ExceptionDetails()}";

                _log.Error(Msg);
                return null;
            }

            return selectList;
        }

        private string GetPatientNameForCohort(int patientRecordId)
        {
            string retVal = "";
            Demographic ptn = (from d in context.Demographics
                               where d.PatientRecordId == patientRecordId
                               select d).FirstOrDefault();
            if (ptn != null)
            {
                retVal = (ptn.lastName ?? "") + ", " + (ptn.firstName ?? "");
                retVal = retVal.Trim();
                if (retVal.Count() == 1 && retVal.Substring(0, 1) == ",")
                {
                    retVal = "";
                }
            }

            return retVal;
        }

        private string SavePatientCohortNew(int patientId_int, int cohortId_int, string started, string terminated,
                                            string doctorId, string notes, int userid, string ipaddress)
        {
            string ret = "";

            try
            {
                PatientCohort cohTest = context.PatientCohorts.
              Where(t => t.PatientId == patientId_int && t.CohortId == cohortId_int).FirstOrDefault();
                if (cohTest != null)
                {
                    return ret;
                }

                PatientCohort p_cohort = new PatientCohort();
                p_cohort.PatientId = patientId_int;
                p_cohort.CohortId = cohortId_int;
                p_cohort.Notes = notes;
                if (started != null && started != "")
                {
                    DateTime dt0 = Convert.ToDateTime(started);
                    p_cohort.Started = dt0;
                }
                if (terminated != null && terminated != "")
                {
                    DateTime dt01 = Convert.ToDateTime(terminated);
                    p_cohort.Terminated = dt01;
                }
                int docId = 0;
                if (int.TryParse(doctorId, out docId))
                {
                    p_cohort.DoctorId = docId;
                }

                context.Entry(p_cohort).State = EntityState.Added;
                context.SaveChanges(userid, ipaddress);
                int patientCohortId = p_cohort.Id;

                ret = patientCohortId.ToString();
            }
            catch (Exception ex)
            {
                string Msg = $" ### RepositoryForAdminUser.SavePatientCohortNew {ex.ExceptionDetails()}";
                _log.Error(Msg);
                ret = "";
            }

            return ret;
        }
        #endregion

        #region address
        public VM_DemAddress SaveNewDemAddress(VM_DemAddress demModel, int userid, string ipaddress)
        {
            //patientRecordId to demographicId
            demModel.dem_demId = context.Demographics.
                    Where(t => t.PatientRecordId == demModel.dem_demId).Select(tt => tt.Id).FirstOrDefault();

            DemographicsAddress addr_ = new DemographicsAddress();
            addr_.addressLine1 = demModel.dem_addressLine1;
            addr_.addressLine2 = demModel.dem_addressLine2;
            addr_.city = demModel.dem_city;
            addr_.country = demModel.dem_country.ToString();
            addr_.addressType = (AddressTypes)demModel.dem_addr_typesId;
            addr_.postalCode = demModel.dem_postalCode;
            addr_.province = EnumExtensions.GetDisplayName(demModel.dem_province);// demModel.dem_province.ToString();
            addr_.DemographicId = demModel.dem_demId;
            addr_.IsActive = true;
            addr_.IsRemoved = false;

            context.Entry(addr_).State = EntityState.Added;

            try
            {
                context.SaveChanges(userid, ipaddress);

                List<name_value_id> addressListRet = GetReturnAddressList(demModel);

                demModel.dem_List = addressListRet;


                demModel.dem_message = "New Demographics Address was created successfully!";
                demModel.dem_id = addr_.Id;
                demModel.success = true;
            }
            catch (Exception ex)
            {
                string Msg = $" ### SaveNewDemAddress {ex.ExceptionDetails()}";
                _log.Error(Msg);

                demModel.success = false;
                demModel.dem_message = "There was a problem to entre new Demographics Address. Contact Administrator! ";
            }

            return demModel;
        }

        public VM_DemAddress UpdateDemAddress(VM_DemAddress demModel, int userid, string ipaddress)
        {
            //demModel.dem_demId = uofw.Demographics.
            //    Where(t => t.Id == demModel.dem_demId).Select(tt => tt.Id).FirstOrDefault();

            //DemographicsAddress addr_ = uofw.DemographicsAddress.
            // Where(t => t.Id == demModel.dem_id).FirstOrDefault();

            //patientRecordId to demographicId
            demModel.dem_demId = context.Demographics.
                Where(t => t.PatientRecordId == demModel.dem_demId).Select(tt => tt.Id).FirstOrDefault();

            DemographicsAddress addr_ = (from demA in context.DemographicsAddress
                                         join dem in context.Demographics on demA.DemographicId equals dem.Id
                                         join pr in context.PatientRecords on dem.PatientRecordId equals pr.Id
                                         where demA.Id == demModel.dem_id && pr.PracticeId == demModel.practiceId
                                         orderby demA.Id descending
                                         select demA).FirstOrDefault();

            if (addr_ == null)
            {
                demModel.dem_message = "Demographics Address is missing in the database.";
                demModel.success = false;
                return demModel;
            }

            addr_.addressLine1 = demModel.dem_addressLine1 ?? "";
            addr_.addressLine2 = demModel.dem_addressLine2 ?? "";
            addr_.city = demModel.dem_city;
            addr_.country = demModel.dem_country.ToString();
            addr_.postalCode = demModel.dem_postalCode.Trim();
            addr_.province = EnumExtensions.GetDisplayName(demModel.dem_province);// demModel.dem_province.ToString();
            addr_.addressType = (AddressTypes)demModel.dem_addr_typesId;

            context.Entry(addr_).State = EntityState.Modified;

            try
            {
                context.SaveChanges(userid, ipaddress);


                List<name_value_id> addressListRet = GetReturnAddressList(demModel);


                demModel.dem_List = addressListRet;


                demModel.dem_message = "New Demographics Address was updated successfully!";
                demModel.success = true;
            }
            catch (Exception ex)
            {
                string Msg = $" ### UpdateDemAddress {ex.ExceptionDetails()}";
                _log.Error(Msg);

                demModel.dem_message = "There was a problem to update Demographics Address. Contact Administrator! ";
                demModel.success = false;
            }

            return demModel;
        }

        private List<name_value_id> GetReturnAddressList(VM_DemAddress demModel)
        {
            List<DemographicsAddress> list_1 = (from demA in context.DemographicsAddress
                                                join dem in context.Demographics on demA.DemographicId equals dem.Id
                                                join pr in context.PatientRecords on dem.PatientRecordId equals pr.Id
                                                where demA.DemographicId == demModel.dem_demId && demA.IsActive && pr.PracticeId == demModel.practiceId
                                                orderby demA.Id descending
                                                select demA).ToList();
            List<name_value_id> tempList = new List<name_value_id>();
            if (list_1 != null && list_1.Count > 0)
            {
                foreach (var item in list_1)
                {
                    name_value_id addF = new name_value_id();
                    addF.id = item.Id;
                    addF.name = (item.addressLine1 ?? "") + " " + (item.addressLine2 ?? "");
                    addF.name1 = item.postalCode ?? "";
                    //addF.name2 = item.province ?? "";   GetProvinceNewString(string province, string country)
                    addF.name2 = GetProvinceNewString(item.province, item.country);
                    addF.name3 = item.country ?? "";
                    addF.name4 = item.city ?? "";
                    addF.value = EnumExtensions.GetDiscriptionName(item.addressType);

                    tempList.Add(addF);
                }
            }

            return tempList;
        }

        public VM_DemAddress GetDemAddress(VM_DemAddress demModel)
        {
            DemographicsAddress ex_a = null;
            //patientRecordId to demographicId
            demModel.dem_demId = context.Demographics.
                Where(t => t.PatientRecordId == demModel.dem_demId).Select(tt => tt.Id).FirstOrDefault();

            if (demModel.dem_type == "load")
            {
                ex_a = (from demA in context.DemographicsAddress
                        join dem in context.Demographics on demA.DemographicId equals dem.Id
                        join pr in context.PatientRecords on dem.PatientRecordId equals pr.Id
                        where demA.DemographicId == demModel.dem_demId && demA.IsActive && pr.PracticeId == demModel.practiceId
                        orderby demA.Id descending
                        select demA).FirstOrDefault();
            }
            else if (demModel.dem_type == "loadById")
            {
                ex_a = (from demA in context.DemographicsAddress
                        join dem in context.Demographics on demA.DemographicId equals dem.Id
                        join pr in context.PatientRecords on dem.PatientRecordId equals pr.Id
                        where demA.Id == demModel.dem_id && demA.IsActive && pr.PracticeId == demModel.practiceId
                        orderby demA.Id descending
                        select demA).FirstOrDefault();
            }



            if (ex_a != null)
            {
                demModel.dem_addressLine1 = ex_a.addressLine1 ?? "";
                demModel.dem_addressLine2 = ex_a.addressLine2 ?? "";
                demModel.dem_city = ex_a.city;
                demModel.dem_country = GetCountry(ex_a.country);
                demModel.dem_postalCode = ex_a.postalCode;
                // demModel.dem_province = GetProvince(ex_a.province);
                demModel.dem_province = GetProvinceNew(ex_a.province, ex_a.country);
                demModel.dem_addr_types = ex_a.addressType;
                demModel.dem_addr_typesId = (int)ex_a.addressType;

                demModel.dem_id = ex_a.Id;
                demModel.dem_message = "";

                List<name_value_id> addressListRet = GetReturnAddressList(demModel);
                demModel.dem_List = addressListRet;

                return demModel;
            }
            else
            {
                return null;
            }
        }
        #endregion

        #region Enrollment

        public VM_Enrollment_n SaveNewEnrollment(VM_Enrollment_n demModel, int userid, string ipaddress)
        {
            int MRP_TblId = (from dmrp in context.DemographicsMainResponsiblePhysicians
                             join dmg in context.Demographics on dmrp.DemographicId equals dmg.Id
                             join pr in context.PatientRecords on dmg.PatientRecordId equals pr.Id
                             where pr.PracticeId == demModel.practiceId
                             && dmrp.PracticeDoctorId == demModel.ed_mrpId
                             && dmg.PatientRecordId == demModel.ed_patientId
                             select dmrp.Id).FirstOrDefault();

            DemographicsEnrollment enr_ = new DemographicsEnrollment();
            if (MRP_TblId != 0)
            {
                //check for unclosed enrollment
                var hasOpenEnrollment = (from enr in context.DemographicsEnrollments
                                         join dmrp in context.DemographicsMainResponsiblePhysicians on enr.DemographicsMRPId equals dmrp.Id
                                         join d in context.Demographics on dmrp.DemographicId equals d.Id
                                         where d.PatientRecordId == demModel.ed_patientId &&
                                               enr.enrollmentTerminationDate == null
                                         select enr).FirstOrDefault();

                if (hasOpenEnrollment == null)
                {
                    enr_.enrolled = demModel.ed_enrolled;
                    enr_.enrollmentDate = CheckIsDateCorrect(demModel.ed_enrollmentDate);
                    //enr_.enrollmentStatusSpecified = demModel.ed_enrollmentStatusSpecified;
                    if (demModel.ed_enrollmentTerminationDate != null)
                    {
                        enr_.enrollmentTerminationDate = CheckIsDateCorrect(demModel.ed_enrollmentTerminationDate);
                    }
                    int termReas = demModel.terminationReasonId ?? 0;
                    enr_.terminationReason = (TerminationReason)termReas;
                    enr_.DemographicsMRPId = MRP_TblId;

                    context.Entry(enr_).State = EntityState.Added;
                }
                else
                {
                    demModel.ed_message = "Patiet has unterminated enrollment. Before adding new enlollment old one has to be terminated!";
                    demModel.success = false;
                }
            }
            else
            {
                demModel.ed_message = "Select Primary Physicion before adding enrollment!";
                demModel.success = false;
            }



            try
            {
                context.SaveChanges(userid, ipaddress);

                demModel.ed_message = "New enrollment was created successfully!";
                demModel.ed_id = enr_.Id;
                demModel.success = true;
            }
            catch (Exception ex)
            {
                string Msg = $" ### SaveNewEnrollment {ex.ExceptionDetails()}";
                _log.Error(Msg);

                demModel.success = false;
                demModel.ed_message = "There was a problem to entre new enrollment. Contact Administrator! ";
            }

            demModel = GetEnrollmentList(demModel);

            return demModel;
        }

        public VM_Enrollment_n UpdateEnrollment(VM_Enrollment_n demModel, int userid, string ipaddress)
        {
            DemographicsEnrollment enrl_ = context.DemographicsEnrollments.
               Where(t => t.Id == demModel.ed_id).FirstOrDefault();
            if (enrl_ == null)
            {
                demModel.ed_message = "Enrollment is missing in the database.";
                demModel.success = false;
                return demModel;
            }

            enrl_.enrolled = demModel.ed_enrolled;
            enrl_.enrollmentDate = CheckIsDateCorrect(demModel.ed_enrollmentDate);
            if (demModel.ed_enrollmentTerminationDate != null)
            {
                enrl_.enrollmentTerminationDate = CheckIsDateCorrect(demModel.ed_enrollmentTerminationDate);
            }
            int termReas = demModel.terminationReasonId ?? 0;
            enrl_.terminationReason = (TerminationReason)termReas;

            context.Entry(enrl_).State = EntityState.Modified;

            try
            {
                context.SaveChanges(userid, ipaddress);

                demModel.ed_message = "New enrollment updated successfully!";
                demModel.success = true;
            }
            catch (Exception ex)
            {
                string Msg = $" ### UpdateEnrollment {ex.ExceptionDetails()}";

                _log.Error(Msg);

                demModel.ed_message = "There was a problem to update enrollment. Contact Administrator! ";
                demModel.success = false;
            }

            demModel = GetEnrollmentList(demModel);

            return demModel;
        }

        public VM_Enrollment_n GetEnrollment(VM_Enrollment_n demModel)
        {
            DemographicsEnrollment enr = null;

            if (demModel.ed_type == "load")
            {
                enr = (from de in context.DemographicsEnrollments
                       join mrp in context.DemographicsMainResponsiblePhysicians on de.DemographicsMRPId equals mrp.Id
                       join dem in context.Demographics on mrp.DemographicId equals dem.Id
                       join pr in context.PatientRecords on dem.PatientRecordId equals pr.Id
                       where
                       //mrp.PracticeDoctorId == demModel.ed_mrpId &&
                            dem.PatientRecordId == demModel.ed_patientId &&
                            pr.PracticeId == demModel.practiceId &&
                            (de.enrollmentTerminationDate == null ||
                            de.enrollmentTerminationDate == DateTime.MinValue ||
                            de.enrollmentTerminationDate == DateTime.MaxValue)
                       orderby de.Id descending
                       select de).FirstOrDefault();
            }
            else if (demModel.ed_type == "loadById")
            {
                enr = context.DemographicsEnrollments.
                            Where(t => t.Id == demModel.ed_id).FirstOrDefault();

                demModel.terminationReasonId = (int)enr.terminationReason;
            }



            if (enr != null)
            {
                demModel.ed_enrolled = true;
                demModel.ed_enrollmentDate = (enr.enrollmentDate != null &&
                        enr.enrollmentDate != DateTime.MaxValue && enr.enrollmentDate != DateTime.MinValue)
                        ? enr.enrollmentDate.Value.ToString("MM/dd/yyyy") : "-";
                demModel.ed_enrollmentTerminationDate = (enr.enrollmentTerminationDate != null &&
                            enr.enrollmentTerminationDate != DateTime.MinValue &&
                            enr.enrollmentTerminationDate != DateTime.MaxValue) ?
                    enr.enrollmentTerminationDate.Value.ToString("MM/dd/yyyy") : null;
                demModel.terminationReasonId = (int)enr.terminationReason;


                demModel.ed_id = enr.Id;
                demModel.ed_message = "";
            }


            demModel = GetEnrollmentList(demModel);




            return demModel;
        }

        private VM_Enrollment_n AddListOfEnrollmentToModel(VM_Enrollment_n demModel)//not in use
        {
            var list_ = (from de in context.DemographicsEnrollments
                         join mrp in context.DemographicsMainResponsiblePhysicians on de.DemographicsMRPId equals mrp.Id
                         join dem in context.Demographics on mrp.DemographicId equals dem.Id
                         join pr in context.PatientRecords on dem.PatientRecordId equals pr.Id
                         where dem.PatientRecordId == demModel.ed_patientId
                                && pr.PracticeId == demModel.practiceId
                         orderby de.DemographicsMRPId descending
                         select de).ToList();

            if (list_ != null && list_.Count > 0)
            {
                List<name_value_id> doc_name_ids = GETMRPNamesList(list_);
                List<name_value_id> list1 = new List<name_value_id>();
                foreach (var item in list_)
                {
                    name_value_id mnd = new name_value_id();
                    mnd.id = item.Id;
                    //enrolled date
                    mnd.name = (item.enrollmentDate != null &&
                    item.enrollmentDate != DateTime.MaxValue && item.enrollmentDate != DateTime.MinValue)
                                        ? item.enrollmentDate.Value.ToString("MM/dd/yyyy") : "-";

                    bool isTerminated = (item.enrollmentTerminationDate != null &&
                        item.enrollmentTerminationDate != DateTime.MinValue &&
                        item.enrollmentTerminationDate != DateTime.MaxValue);
                    //terminated date
                    mnd.name1 = isTerminated ? item.enrollmentTerminationDate.Value.ToString("MM/dd/yyyy") : "";
                    mnd.value = GetTerminationName(item.terminationReason);
                    if (!isTerminated)
                    {
                        demModel.isThereUnterminatedEnrl = true;
                        mnd.value = "";
                    }
                    //mnd.name2 = MRPName;
                    mnd.name2 = doc_name_ids.Where(t => t.id == item.DemographicsMRPId).Select(tt => tt.name).FirstOrDefault();

                    list1.Add(mnd);
                }
                demModel.ed_List = list1;
            }

            return demModel;
        }

        public VM_Enrollment_n GetEnrollmentList(VM_Enrollment_n demModel)
        {
            var list_ = (from de in context.DemographicsEnrollments
                         join mrp in context.DemographicsMainResponsiblePhysicians on de.DemographicsMRPId equals mrp.Id
                         join dem in context.Demographics on mrp.DemographicId equals dem.Id
                         join pr in context.PatientRecords on dem.PatientRecordId equals pr.Id
                         where dem.PatientRecordId == demModel.ed_patientId
                                && pr.PracticeId == demModel.practiceId
                         orderby de.DemographicsMRPId descending
                         select de).ToList();

            if (list_ != null && list_.Count > 0)
            {
                List<name_value_id> doc_name_ids = GETMRPNamesList(list_);
                List<name_value_id> list1 = new List<name_value_id>();
                foreach (var item in list_)
                {
                    name_value_id mnd = new name_value_id();
                    mnd.id = item.Id;
                    //enrolled date
                    mnd.name = (item.enrollmentDate != null &&
                    item.enrollmentDate != DateTime.MaxValue && item.enrollmentDate != DateTime.MinValue)
                                        ? item.enrollmentDate.Value.ToString("MM/dd/yyyy") : "-";

                    bool isTerminated = (item.enrollmentTerminationDate != null &&
                        item.enrollmentTerminationDate != DateTime.MinValue &&
                        item.enrollmentTerminationDate != DateTime.MaxValue);
                    //terminated date
                    mnd.name1 = isTerminated ? item.enrollmentTerminationDate.Value.ToString("MM/dd/yyyy") : "";
                    mnd.value = GetTerminationName(item.terminationReason);
                    if (!isTerminated)
                    {
                        demModel.isThereUnterminatedEnrl = true;
                        mnd.value = "";
                    }
                    //mnd.name2 = MRPName;
                    mnd.name2 = doc_name_ids.Where(t => t.id == item.DemographicsMRPId).Select(tt => tt.name).FirstOrDefault();

                    list1.Add(mnd);
                }
                demModel.ed_List = list1;
            }

            return demModel;
        }

        private List<name_value_id> GETMRPNamesList(List<DemographicsEnrollment> list_)
        {
            List<name_value_id> retList = new List<name_value_id>();
            List<int> ids = list_.Select(t => t.DemographicsMRPId).ToList();
            var retList1 = (from dmrp in context.DemographicsMainResponsiblePhysicians
                            join ex in context.ExternalDoctors on dmrp.ExternalDoctorId equals ex.Id
                            where ids.Contains(dmrp.Id)
                            select new name_value_id
                            {
                                id = dmrp.Id,
                                name = (ex.lastName ?? "") + ", " + (ex.firstName ?? "")
                            }).ToList();
            if (retList1 != null)
            {
                retList = retList1;
            }


            return retList;
        }

        private string GetMRPName(int ed_mrpId)
        {
            string retVal = "";
            ExternalDoctor doctor = (from ex in context.ExternalDoctors
                                     join pr in context.PracticeDoctors on ex.Id equals pr.ExternalDoctorId
                                     where pr.Id == ed_mrpId
                                     select ex).FirstOrDefault();
            if (doctor != null)
            {
                retVal = (doctor.lastName ?? "") + ", " + (doctor.firstName ?? "");
            }

            return retVal;
        }

        private string GetTerminationName(TerminationReason terminationReason)
        {
            string strRet = "";
            try
            {
                strRet = EnumExtension.GetEnumDescription(terminationReason);
            }
            catch (Exception)
            {
            }

            return strRet;
        }

        private DateTime? CheckIsDateCorrect(string date)
        {
            DateTime? return_ = null;
            try
            {
                return_ = Convert.ToDateTime(date);
            }
            catch (FormatException)
            {
            }

            return return_;
        }
        #endregion
        #endregion

        internal int? GetYearOfBirth(string[] names)
        {
            int? yearOfBirth = null;

            if (names.Length == 3)
            {
                bool isNumeric = int.TryParse(names[2].Trim(), out _);
                if (isNumeric && names[2].Trim().Length == 4)
                {
                    yearOfBirth = int.Parse(names[2].Trim());
                }
            }
            return yearOfBirth;
        }
        private List<SqlParameter> GetSearchParameters(int practiceId, string searchString, int searchType, int topResults, int? active)
        {
            List<SqlParameter> parms = new List<SqlParameter>();
            if (searchType == 1)
            {
                if (searchString.Contains(","))
                {
                    string[] names = searchString.Split(',');
                    string lastname = names[0].Trim();
                    string firstname = names[1].Trim();
                    int? yearOfBirth = GetYearOfBirth(names);

                    if ((!string.IsNullOrWhiteSpace(lastname)) && (string.IsNullOrWhiteSpace(firstname)))
                    {
                        parms = new List<SqlParameter>
                        {
                            new SqlParameter("PracticeId",practiceId),
                            new SqlParameter("LastName",lastname),
                            new SqlParameter("FirstName",null),
                            new SqlParameter("OHIP",null),
                            new SqlParameter("TOPResult",topResults),
                            new SqlParameter("Active",active),
                            new SqlParameter("YearOfBirth",null),
                            new SqlParameter("PhoneNumber",null),
                            new SqlParameter("PatientId",null),
                            new SqlParameter("DateOfBirth",null)
                        };
                    }
                    else if ((string.IsNullOrWhiteSpace(lastname)) && (!string.IsNullOrWhiteSpace(firstname)))
                    {
                        parms = new List<SqlParameter>
                        {
                            new SqlParameter("PracticeId",practiceId),
                            new SqlParameter("LastName",null),
                            new SqlParameter("FirstName",firstname),
                            new SqlParameter("OHIP",null),
                            new SqlParameter("TOPResult",topResults),
                            new SqlParameter("Active",active),
                            new SqlParameter("YearOfBirth",null),
                            new SqlParameter("PhoneNumber",null),
                            new SqlParameter("PatientId",null),
                            new SqlParameter("DateOfBirth",null)
                        };
                    }
                    else
                    {
                        parms = new List<SqlParameter>
                        {
                            new SqlParameter("PracticeId",practiceId),
                            new SqlParameter("LastName",lastname),
                            new SqlParameter("FirstName",firstname),
                            new SqlParameter("OHIP",null),
                            new SqlParameter("TOPResult",topResults),
                            new SqlParameter("Active",active),
                            new SqlParameter("YearOfBirth",yearOfBirth),
                            new SqlParameter("PhoneNumber",null),
                            new SqlParameter("PatientId",null),
                            new SqlParameter("DateOfBirth",null)
                        };
                    }
                }
                else
                {
                    parms = new List<SqlParameter>
                    {
                        new SqlParameter("PracticeId",practiceId),
                        new SqlParameter("LastName",searchString),
                        new SqlParameter("FirstName",searchString),
                        new SqlParameter("OHIP",null),
                        new SqlParameter("TOPResult",topResults),
                        new SqlParameter("Active",active),
                        new SqlParameter("YearOfBirth",null),
                        new SqlParameter("PhoneNumber",null),
                        new SqlParameter("PatientId",null),
                        new SqlParameter("DateOfBirth",null)
                    };
                }
            }
            else if (searchType == 2 || searchType == 3 || searchType == 4)
            {
                // if search by phone number - removed all chars not digits
                if (searchType == 3)
                {
                    searchString = new String(searchString.Where(Char.IsDigit).ToArray());
                }

                bool isNumeric = long.TryParse(searchString, out _);
                if (isNumeric)
                {
                    if (searchType == 2)
                    {
                        parms = new List<SqlParameter>
                        {
                            new SqlParameter("PracticeId",practiceId),
                            new SqlParameter("LastName",null),
                            new SqlParameter("FirstName",null),
                            new SqlParameter("OHIP",searchString),
                            new SqlParameter("TOPResult",topResults),
                            new SqlParameter("Active",active),
                            new SqlParameter("YearOfBirth",null),
                            new SqlParameter("PhoneNumber",null),
                            new SqlParameter("PatientId",null),
                            new SqlParameter("DateOfBirth",null)
                        };
                    }
                    else if (searchType == 3)
                    {
                        parms = new List<SqlParameter>
                        {
                            new SqlParameter("PracticeId",practiceId),
                            new SqlParameter("LastName",null),
                            new SqlParameter("FirstName",null),
                            new SqlParameter("OHIP",null),
                            new SqlParameter("TOPResult",topResults),
                            new SqlParameter("Active",active),
                            new SqlParameter("YearOfBirth",null),
                            new SqlParameter("PhoneNumber",searchString),
                            new SqlParameter("PatientId",null),
                            new SqlParameter("DateOfBirth",null)
                        };
                    }
                    else
                    {
                        parms = new List<SqlParameter>
                        {
                            new SqlParameter("PracticeId",practiceId),
                            new SqlParameter("LastName",null),
                            new SqlParameter("FirstName",null),
                            new SqlParameter("OHIP",null),
                            new SqlParameter("TOPResult",topResults),
                            new SqlParameter("Active",active),
                            new SqlParameter("YearOfBirth",null),
                            new SqlParameter("PhoneNumber",null),
                            new SqlParameter("PatientId",searchString),
                            new SqlParameter("DateOfBirth",null)
                        };
                    }
                }
            }
            else if (searchType == 5)
            {
                DateTime? dtDateOfBirth = GetDateFromString(searchString);

                parms = new List<SqlParameter>
                {
                    new SqlParameter("PracticeId",practiceId),
                    new SqlParameter("LastName",null),
                    new SqlParameter("FirstName",null),
                    new SqlParameter("OHIP",null),
                    new SqlParameter("TOPResult",topResults),
                    new SqlParameter("Active",active),
                    new SqlParameter("YearOfBirth",null),
                    new SqlParameter("PhoneNumber",null),
                    new SqlParameter("PatientId",null),
                    new SqlParameter("DateOfBirth",dtDateOfBirth)
                };
            }
            return parms;
        }
        private DateTime? GetDateFromString(string date)
        {
            try
            {
                if (date?.Length == 10)
                {
                    if (date.Contains("/"))
                    {
                        DateTime birthday = DateTime.ParseExact(date, "MM/dd/yyyy", CultureInfo.InvariantCulture);
                        return birthday;
                    }
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex.Message);
            }

            return null;
        }
        internal bool IsNotAllowedSearchTerm(string searchString)
        {
            return (!string.IsNullOrWhiteSpace(searchString) && searchString.Length > 50);
        }
        private List<VMPatientSearchResult> GetPatientSearchResult(List<SqlParameter> parms)
        {
            return context.GetData<VMPatientSearchResult>("[dbo].[SP_Find_Patients_V1]", parms).ToList();
        }
        public void Dispose()
        {
            if (_externalDoctorBll != null) _externalDoctorBll.Dispose();
            context.Dispose();
        }
    }
}
