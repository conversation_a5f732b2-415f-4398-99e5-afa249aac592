@using Cerebrum.ViewModels.Patient
﻿@model IEnumerable<Cerebrum.ViewModels.Patient.VMPatientInfo>

<table id="tbl-patient-search" class="table table-bordered">
    <tr>
        <th>
            @Html.DisplayNameFor(model => model.LastName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.FirstName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.MiddleName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.DateOfBirth)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.OHIP)
        </th>
        <th></th>
    </tr>
    @foreach (var item in Model)
    {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.LastName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.FirstName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.MiddleName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.DateOfBirth)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.OHIP) @Html.DisplayFor(modelItem => item.OHIPVersionCode)
            </td>
            <td>
                <div class="btn-popover-container">
                    <button type="button" class="btn btn-default btn-xs popover-btn">
                        <span class="glyphicon glyphicon-option-vertical text-primary"></span>
                    </button>
                    <div class="btn-popover-title">
                        <span class="default-text-color">Patient Menu</span>
                    </div>
                    <div class="btn-popover-content">
                        <div class="patient-menu-placeholder" data-patient-id="@item.PatientId">
                            <!-- Patient menu will be loaded via AJAX -->
                        </div>
                    </div>
                </div>
            </td>
        </tr>

</table>
